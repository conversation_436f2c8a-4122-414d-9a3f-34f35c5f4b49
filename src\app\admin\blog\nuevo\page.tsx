'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { BlogPost, User } from '@/types'

interface BlogFormData {
  title: string
  slug: string
  content: string
  excerpt: string
  category: 'noticias' | 'eventos' | 'musica' | 'entrevistas' | 'colaboraciones'
  tags: string[]
  published: boolean
  featured: boolean
}

export default function NewBlogPostPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>('')
  const [formData, setFormData] = useState<BlogFormData>({
    title: '',
    slug: '',
    content: '',
    excerpt: '',
    category: 'noticias',
    tags: [],
    published: false,
    featured: false
  })
  const router = useRouter()

  useEffect(() => {
    checkAccess()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'periodista', 'moderador'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[áàäâ]/g, 'a')
      .replace(/[éèëê]/g, 'e')
      .replace(/[íìïî]/g, 'i')
      .replace(/[óòöô]/g, 'o')
      .replace(/[úùüû]/g, 'u')
      .replace(/[ñ]/g, 'n')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({ ...prev, [name]: checked }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
      
      // Auto-generar slug cuando cambia el título
      if (name === 'title') {
        setFormData(prev => ({ ...prev, slug: generateSlug(value) }))
      }
    }
  }

  const handleTagsChange = (value: string) => {
    const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    setFormData(prev => ({ ...prev, tags }))
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setImageFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const uploadImage = async (file: File) => {
    const fileExt = file.name.split('.').pop()
    const fileName = `blog/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from('blog-images')
      .upload(fileName, file)

    if (error) throw error

    const { data: { publicUrl } } = supabase.storage
      .from('blog-images')
      .getPublicUrl(fileName)

    return publicUrl
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let featuredImageUrl = ''

      // Subir imagen destacada si existe
      if (imageFile) {
        setUploading(true)
        featuredImageUrl = await uploadImage(imageFile)
      }

      // Crear el post en la base de datos
      const postData = {
        ...formData,
        featured_image: featuredImageUrl || null,
        author_id: currentUser?.id,
        published_at: formData.published ? new Date().toISOString() : null
      }

      const { error } = await supabase
        .from('blog_posts')
        .insert(postData)

      if (error) throw error

      router.push('/admin/blog')
    } catch (error) {
      console.error('Error creating blog post:', error)
      alert('Error al crear el post')
    } finally {
      setLoading(false)
      setUploading(false)
    }
  }

  const categories = [
    { value: 'noticias', label: 'Noticias' },
    { value: 'eventos', label: 'Eventos' },
    { value: 'musica', label: 'Música' },
    { value: 'entrevistas', label: 'Entrevistas' },
    { value: 'colaboraciones', label: 'Colaboraciones' }
  ]

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Nuevo Post</h1>
              <p className="text-gray-400">Crear un nuevo artículo para el blog</p>
            </div>
            <Button variant="ghost" onClick={() => router.push('/admin/blog')}>
              ← Volver al Blog
            </Button>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Contenido Principal */}
              <div className="lg:col-span-2 space-y-6">
                <Card variant="glass" className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">Contenido</h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Título *
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                        placeholder="Título del artículo"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Slug (URL)
                      </label>
                      <input
                        type="text"
                        name="slug"
                        value={formData.slug}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                        placeholder="url-del-articulo"
                      />
                      <p className="text-xs text-gray-400 mt-1">
                        URL: /blog/{formData.slug}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Extracto
                      </label>
                      <textarea
                        name="excerpt"
                        value={formData.excerpt}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                        placeholder="Breve descripción del artículo..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Contenido *
                      </label>
                      <textarea
                        name="content"
                        value={formData.content}
                        onChange={handleInputChange}
                        required
                        rows={15}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                        placeholder="Escribe el contenido del artículo aquí..."
                      />
                      <p className="text-xs text-gray-400 mt-1">
                        Puedes usar Markdown para formatear el texto
                      </p>
                    </div>
                  </div>
                </Card>

                {/* Imagen Destacada */}
                <Card variant="glass" className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">Imagen Destacada</h2>
                  
                  <div>
                    <input
                      type="file"
                      accept="image/jpeg,image/png,image/webp,image/gif"
                      onChange={handleImageChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-drago-red file:text-white hover:file:bg-drago-red-dark"
                    />
                    
                    {imagePreview && (
                      <div className="mt-4">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-48 object-cover rounded-lg"
                        />
                      </div>
                    )}
                  </div>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                <Card variant="glass" className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">Configuración</h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Categoría
                      </label>
                      <select
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      >
                        {categories.map(category => (
                          <option key={category.value} value={category.value}>
                            {category.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Tags (separar con comas)
                      </label>
                      <input
                        type="text"
                        value={formData.tags.join(', ')}
                        onChange={(e) => handleTagsChange(e.target.value)}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                        placeholder="reggaeton, música, chile"
                      />
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="published"
                          checked={formData.published}
                          onChange={handleInputChange}
                          className="mr-2"
                        />
                        <label className="text-sm text-gray-300">
                          Publicar inmediatamente
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="featured"
                          checked={formData.featured}
                          onChange={handleInputChange}
                          className="mr-2"
                        />
                        <label className="text-sm text-gray-300">
                          Post destacado
                        </label>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Preview */}
                {formData.title && (
                  <Card variant="glass" className="p-6">
                    <h2 className="text-xl font-bold text-white mb-4">Preview</h2>
                    
                    <div className="space-y-3">
                      <h3 className="text-lg font-semibold text-white">
                        {formData.title}
                      </h3>
                      
                      {formData.excerpt && (
                        <p className="text-gray-300 text-sm">
                          {formData.excerpt}
                        </p>
                      )}
                      
                      <div className="flex flex-wrap gap-2">
                        <span className="px-2 py-1 bg-drago-red text-white text-xs rounded">
                          {categories.find(c => c.value === formData.category)?.label}
                        </span>
                        {formData.featured && (
                          <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">
                            Destacado
                          </span>
                        )}
                      </div>
                      
                      {formData.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {formData.tags.map((tag, index) => (
                            <span key={index} className="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </Card>
                )}

                {/* Botones */}
                <div className="space-y-3">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={loading || uploading}
                    glow
                    className="w-full"
                  >
                    {uploading ? 'Subiendo imagen...' : loading ? 'Guardando...' : 'Crear Post'}
                  </Button>
                  
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => router.push('/admin/blog')}
                    className="w-full"
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </Container>
      </main>
    </div>
  )
}
