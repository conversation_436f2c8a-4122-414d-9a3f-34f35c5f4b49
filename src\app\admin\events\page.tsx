'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { Event, User } from '@/types'

export default function EventsAdminPage() {
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'ongoing' | 'completed' | 'cancelled'>('all')
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const router = useRouter()

  useEffect(() => {
    checkAccess()
    loadEvents()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'booking'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const loadEvents = async () => {
    try {
      const { data, error } = await supabase
        .from('events')
        .select(`
          *,
          creator:profiles!events_created_by_fkey(nombres, apellidos, role)
        `)
        .order('event_date', { ascending: false })

      if (error) throw error
      setEvents(data || [])
    } catch (error) {
      console.error('Error loading events:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteEvent = async (eventId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este evento?')) return

    try {
      const { error } = await supabase
        .from('events')
        .delete()
        .eq('id', eventId)

      if (error) throw error
      loadEvents()
    } catch (error) {
      console.error('Error deleting event:', error)
    }
  }

  const handleUpdateStatus = async (eventId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('events')
        .update({ status: newStatus })
        .eq('id', eventId)

      if (error) throw error
      loadEvents()
    } catch (error) {
      console.error('Error updating event status:', error)
    }
  }

  const filteredEvents = events.filter(event => {
    if (filter === 'all') return true
    return event.status === filter
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'text-blue-400'
      case 'ongoing': return 'text-green-400'
      case 'completed': return 'text-gray-400'
      case 'cancelled': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-600'
      case 'ongoing': return 'bg-green-600'
      case 'completed': return 'bg-gray-600'
      case 'cancelled': return 'bg-red-600'
      default: return 'bg-gray-600'
    }
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'concierto': return 'bg-purple-600'
      case 'festival': return 'bg-orange-600'
      case 'colaboracion': return 'bg-pink-600'
      case 'entrevista': return 'bg-blue-600'
      default: return 'bg-gray-600'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('es-CL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isEventPast = (dateString: string) => {
    return new Date(dateString) < new Date()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando eventos...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Gestión de Eventos</h1>
              <p className="text-gray-400">Administra conciertos, fechas y venues</p>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/events/new">
                <Button variant="primary" glow>
                  + Nuevo Evento
                </Button>
              </Link>
              <Button variant="ghost" onClick={() => router.push('/admin')}>
                ← Dashboard
              </Button>
            </div>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          {/* Filtros */}
          <div className="flex flex-wrap gap-4 mb-8">
            {[
              { key: 'all', label: 'Todos', count: events.length },
              { key: 'upcoming', label: 'Próximos', count: events.filter(e => e.status === 'upcoming').length },
              { key: 'ongoing', label: 'En curso', count: events.filter(e => e.status === 'ongoing').length },
              { key: 'completed', label: 'Completados', count: events.filter(e => e.status === 'completed').length },
              { key: 'cancelled', label: 'Cancelados', count: events.filter(e => e.status === 'cancelled').length }
            ].map(filterOption => (
              <Button
                key={filterOption.key}
                variant={filter === filterOption.key ? 'primary' : 'ghost'}
                onClick={() => setFilter(filterOption.key as any)}
                size="sm"
              >
                {filterOption.label} ({filterOption.count})
              </Button>
            ))}
          </div>

          {/* Lista de Eventos */}
          <div className="space-y-4">
            {filteredEvents.map(event => (
              <Card key={event.id} variant="glass" className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex items-start gap-4 flex-1">
                    {/* Poster */}
                    <div className="w-20 h-20 bg-drago-black-light rounded-lg flex items-center justify-center overflow-hidden">
                      {event.poster_url ? (
                        <img src={event.poster_url} alt={event.title} className="w-full h-full object-cover" />
                      ) : (
                        <span className="text-2xl">🎪</span>
                      )}
                    </div>

                    {/* Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-white">
                          {event.title}
                        </h3>
                        <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getEventTypeColor(event.event_type)}`}>
                          {event.event_type}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getStatusBadgeColor(event.status)}`}>
                          {event.status}
                        </span>
                        {isEventPast(event.event_date) && event.status === 'upcoming' && (
                          <span className="px-2 py-1 rounded text-xs font-semibold text-white bg-yellow-600">
                            Vencido
                          </span>
                        )}
                      </div>
                      
                      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-300 mb-3">
                        <div>
                          <span className="text-gray-400">📅 Fecha:</span> {formatDate(event.event_date)}
                        </div>
                        {event.venue && (
                          <div>
                            <span className="text-gray-400">📍 Venue:</span> {event.venue}
                          </div>
                        )}
                        {event.city && (
                          <div>
                            <span className="text-gray-400">🌍 Ciudad:</span> {event.city}, {event.country}
                          </div>
                        )}
                        {event.capacity && (
                          <div>
                            <span className="text-gray-400">👥 Capacidad:</span> {event.capacity.toLocaleString()}
                          </div>
                        )}
                        {event.price_range && (
                          <div>
                            <span className="text-gray-400">💰 Precio:</span> {event.price_range}
                          </div>
                        )}
                      </div>

                      {event.description && (
                        <p className="text-gray-300 text-sm line-clamp-2 mb-2">
                          {event.description}
                        </p>
                      )}

                      {event.ticket_url && (
                        <a
                          href={event.ticket_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-drago-red hover:text-drago-red-dark text-sm"
                        >
                          🎫 Ver tickets
                        </a>
                      )}
                    </div>
                  </div>

                  {/* Acciones */}
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Link href={`/admin/events/edit/${event.id}`}>
                      <Button variant="ghost" size="sm">
                        ✏️ Editar
                      </Button>
                    </Link>
                    
                    {/* Cambiar estado */}
                    <select
                      value={event.status}
                      onChange={(e) => handleUpdateStatus(event.id, e.target.value)}
                      className="px-3 py-1 bg-drago-black-light border border-gray-600 text-white rounded text-sm"
                    >
                      <option value="upcoming">Próximo</option>
                      <option value="ongoing">En curso</option>
                      <option value="completed">Completado</option>
                      <option value="cancelled">Cancelado</option>
                    </select>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteEvent(event.id)}
                      className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                    >
                      🗑️ Eliminar
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {filteredEvents.length === 0 && (
            <Card variant="glass" className="p-8 text-center">
              <p className="text-gray-400 mb-4">No hay eventos que coincidan con el filtro seleccionado.</p>
              <Link href="/admin/events/new">
                <Button variant="primary">
                  Crear el primer evento
                </Button>
              </Link>
            </Card>
          )}
        </Container>
      </main>
    </div>
  )
}
