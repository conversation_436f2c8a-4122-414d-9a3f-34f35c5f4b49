'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { Song, BlogPost, Event, User } from '@/types'

interface AnalyticsData {
  totalSongs: number
  totalPosts: number
  totalEvents: number
  totalStreams: number
  monthlyGrowth: number
  topSongs: Array<Song & { total_streams: number }>
  recentActivity: Array<{
    type: string
    title: string
    date: string
    metric: string
  }>
  streamingPlatforms: {
    spotify: number
    youtube: number
    apple_music: number
  }
}

export default function AnalyticsPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalSongs: 0,
    totalPosts: 0,
    totalEvents: 0,
    totalStreams: 0,
    monthlyGrowth: 0,
    topSongs: [],
    recentActivity: [],
    streamingPlatforms: {
      spotify: 0,
      youtube: 0,
      apple_music: 0
    }
  })
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')
  const router = useRouter()

  useEffect(() => {
    checkAccess()
    loadAnalytics()
  }, [timeRange])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || profile.role !== 'admin' || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const loadAnalytics = async () => {
    try {
      // Cargar datos básicos
      const [songsResult, postsResult, eventsResult] = await Promise.all([
        supabase.from('songs').select('*', { count: 'exact' }),
        supabase.from('blog_posts').select('*', { count: 'exact' }),
        supabase.from('events').select('*', { count: 'exact' })
      ])

      // Cargar canciones con streams
      const { data: topSongs } = await supabase
        .from('songs')
        .select('*')
        .eq('status', 'published')
        .order('spotify_streams', { ascending: false })
        .limit(5)

      // Calcular totales de streams
      const totalSpotifyStreams = topSongs?.reduce((sum, song) => sum + (song.spotify_streams || 0), 0) || 0
      const totalYoutubeViews = topSongs?.reduce((sum, song) => sum + (song.youtube_views || 0), 0) || 0
      const totalAppleMusicStreams = topSongs?.reduce((sum, song) => sum + (song.apple_music_streams || 0), 0) || 0

      // Simular actividad reciente (en una implementación real, esto vendría de analytics_metrics)
      const recentActivity = [
        {
          type: 'song_play',
          title: 'FULL PIOLI 2.O',
          date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          metric: '+1.2K reproducciones'
        },
        {
          type: 'blog_view',
          title: 'Nueva colaboración anunciada',
          date: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          metric: '+340 vistas'
        },
        {
          type: 'song_play',
          title: 'TOTA',
          date: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          metric: '+890 reproducciones'
        },
        {
          type: 'event_view',
          title: 'Concierto Santiago 2024',
          date: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          metric: '+156 vistas'
        }
      ]

      setAnalytics({
        totalSongs: songsResult.count || 0,
        totalPosts: postsResult.count || 0,
        totalEvents: eventsResult.count || 0,
        totalStreams: totalSpotifyStreams + totalYoutubeViews + totalAppleMusicStreams,
        monthlyGrowth: 12.5, // Simulado
        topSongs: topSongs?.map(song => ({
          ...song,
          total_streams: (song.spotify_streams || 0) + (song.youtube_views || 0) + (song.apple_music_streams || 0)
        })) || [],
        recentActivity,
        streamingPlatforms: {
          spotify: totalSpotifyStreams,
          youtube: totalYoutubeViews,
          apple_music: totalAppleMusicStreams
        }
      })
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'song_play': return '🎵'
      case 'blog_view': return '📝'
      case 'event_view': return '🎪'
      default: return '📊'
    }
  }

  const timeRangeLabels = {
    '7d': 'Últimos 7 días',
    '30d': 'Últimos 30 días',
    '90d': 'Últimos 3 meses',
    '1y': 'Último año'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando analytics...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Analytics & Estadísticas</h1>
              <p className="text-gray-400">Métricas de rendimiento y engagement</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as any)}
                className="px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
              >
                {Object.entries(timeRangeLabels).map(([value, label]) => (
                  <option key={value} value={value}>{label}</option>
                ))}
              </select>
              <Button variant="ghost" onClick={() => router.push('/admin')}>
                ← Dashboard
              </Button>
            </div>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          {/* Métricas Principales */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Streams</p>
                  <p className="text-3xl font-bold text-white">{formatNumber(analytics.totalStreams)}</p>
                  <p className="text-green-400 text-sm">+{analytics.monthlyGrowth}% este mes</p>
                </div>
                <div className="text-3xl">📊</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Canciones</p>
                  <p className="text-3xl font-bold text-white">{analytics.totalSongs}</p>
                  <p className="text-blue-400 text-sm">Total en catálogo</p>
                </div>
                <div className="text-3xl">🎵</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Posts del Blog</p>
                  <p className="text-3xl font-bold text-white">{analytics.totalPosts}</p>
                  <p className="text-purple-400 text-sm">Artículos publicados</p>
                </div>
                <div className="text-3xl">📝</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Eventos</p>
                  <p className="text-3xl font-bold text-white">{analytics.totalEvents}</p>
                  <p className="text-orange-400 text-sm">Shows programados</p>
                </div>
                <div className="text-3xl">🎪</div>
              </div>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 mb-12">
            {/* Top Canciones */}
            <Card variant="glass" className="p-6">
              <h2 className="text-xl font-bold text-white mb-6">Top Canciones</h2>
              <div className="space-y-4">
                {analytics.topSongs.map((song, index) => (
                  <div key={song.id} className="flex items-center gap-4 p-3 bg-drago-black-light rounded-lg">
                    <div className="w-8 h-8 bg-drago-red rounded-full flex items-center justify-center text-white font-bold">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white font-semibold">{song.title}</h3>
                      <p className="text-gray-400 text-sm">{song.artist}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-semibold">{formatNumber(song.total_streams)}</p>
                      <p className="text-gray-400 text-sm">streams</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Plataformas de Streaming */}
            <Card variant="glass" className="p-6">
              <h2 className="text-xl font-bold text-white mb-6">Plataformas de Streaming</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-drago-black-light rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold">S</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">Spotify</h3>
                      <p className="text-gray-400 text-sm">Streams</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-semibold">{formatNumber(analytics.streamingPlatforms.spotify)}</p>
                    <div className="w-24 bg-gray-700 rounded-full h-2 mt-1">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${(analytics.streamingPlatforms.spotify / analytics.totalStreams) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-drago-black-light rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold">Y</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">YouTube</h3>
                      <p className="text-gray-400 text-sm">Views</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-semibold">{formatNumber(analytics.streamingPlatforms.youtube)}</p>
                    <div className="w-24 bg-gray-700 rounded-full h-2 mt-1">
                      <div 
                        className="bg-red-600 h-2 rounded-full" 
                        style={{ width: `${(analytics.streamingPlatforms.youtube / analytics.totalStreams) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-drago-black-light rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold">A</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">Apple Music</h3>
                      <p className="text-gray-400 text-sm">Streams</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-semibold">{formatNumber(analytics.streamingPlatforms.apple_music)}</p>
                    <div className="w-24 bg-gray-700 rounded-full h-2 mt-1">
                      <div 
                        className="bg-gray-600 h-2 rounded-full" 
                        style={{ width: `${(analytics.streamingPlatforms.apple_music / analytics.totalStreams) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Actividad Reciente */}
          <Card variant="glass" className="p-6">
            <h2 className="text-xl font-bold text-white mb-6">Actividad Reciente</h2>
            <div className="space-y-4">
              {analytics.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-4 p-3 bg-drago-black-light rounded-lg">
                  <div className="text-2xl">{getActivityIcon(activity.type)}</div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold">{activity.title}</h3>
                    <p className="text-gray-400 text-sm">
                      {new Date(activity.date).toLocaleString('es-CL')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-drago-red font-semibold">{activity.metric}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Acciones Rápidas */}
          <div className="grid md:grid-cols-3 gap-6 mt-8">
            <Card variant="glass" className="p-6 text-center">
              <div className="text-4xl mb-4">📈</div>
              <h3 className="text-lg font-semibold text-white mb-2">Exportar Datos</h3>
              <p className="text-gray-400 text-sm mb-4">Descargar reporte completo</p>
              <Button variant="outline" size="sm">
                Exportar CSV
              </Button>
            </Card>

            <Card variant="glass" className="p-6 text-center">
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-lg font-semibold text-white mb-2">Configurar Metas</h3>
              <p className="text-gray-400 text-sm mb-4">Establecer objetivos mensuales</p>
              <Button variant="outline" size="sm">
                Configurar
              </Button>
            </Card>

            <Card variant="glass" className="p-6 text-center">
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-lg font-semibold text-white mb-2">Reportes Avanzados</h3>
              <p className="text-gray-400 text-sm mb-4">Análisis detallado</p>
              <Button variant="outline" size="sm">
                Ver Reportes
              </Button>
            </Card>
          </div>
        </Container>
      </main>
    </div>
  )
}
