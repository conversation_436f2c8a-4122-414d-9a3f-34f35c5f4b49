'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'

export default function LoginPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  useEffect(() => {
    // Verificar si ya está autenticado
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (user) {
      // Verificar si es admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (profile?.role === 'admin') {
        router.push('/admin')
      } else {
        router.push('/')
      }
    }
  }

  const handleGoogleLogin = async () => {
    try {
      setLoading(true)
      setError('')

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      })

      if (error) {
        setError(error.message)
      }
    } catch (error) {
      setError('Error al iniciar sesión con Google')
    } finally {
      setLoading(false)
    }
  }

  const handleEmailLogin = async (email: string, password: string) => {
    try {
      setLoading(true)
      setError('')

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        setError(error.message)
      } else {
        // Verificar rol después del login
        const { data: { user } } = await supabase.auth.getUser()
        if (user) {
          const { data: profile } = await supabase
            .from('profiles')
            .select('role')
            .eq('id', user.id)
            .single()

          if (profile?.role === 'admin') {
            router.push('/admin')
          } else {
            setError('No tienes permisos de administrador')
            await supabase.auth.signOut()
          }
        }
      }
    } catch (error) {
      setError('Error al iniciar sesión')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-drago-black flex items-center justify-center">
      <Container size="sm">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Panel de <span className="text-drago-red">Administración</span>
          </h1>
          <p className="text-gray-400">
            Acceso exclusivo para administradores de Drago200
          </p>
        </div>

        <Card variant="glass" className="p-8 max-w-md mx-auto">
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-gradient-red-gold rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-white">D200</span>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Iniciar Sesión</h2>
            <p className="text-gray-400">Accede con tu cuenta autorizada</p>
          </div>

          {error && (
            <div className="bg-red-500/20 border border-red-500 text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          <div className="space-y-6">
            {/* Google Login */}
            <Button
              onClick={handleGoogleLogin}
              disabled={loading}
              size="lg"
              variant="outline"
              className="w-full flex items-center justify-center space-x-3"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>{loading ? 'Conectando...' : 'Continuar con Google'}</span>
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-600"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-drago-black-light text-gray-400">o</span>
              </div>
            </div>

            {/* Email Login Form */}
            <EmailLoginForm onSubmit={handleEmailLogin} loading={loading} />
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-400 text-sm">
              ¿No tienes acceso? Contacta al administrador principal
            </p>
          </div>
        </Card>

        <div className="text-center mt-8">
          <Button variant="ghost" onClick={() => router.push('/')}>
            ← Volver al sitio web
          </Button>
        </div>
      </Container>
    </div>
  )
}

function EmailLoginForm({ onSubmit, loading }: { onSubmit: (email: string, password: string) => void, loading: boolean }) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(email, password)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-white font-semibold mb-2">
          Email
        </label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
          required
          className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
        />
      </div>

      <div>
        <label className="block text-white font-semibold mb-2">
          Contraseña
        </label>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="••••••••"
          required
          className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
        />
      </div>

      <Button
        type="submit"
        disabled={loading}
        size="lg"
        glow
        className="w-full"
      >
        {loading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
      </Button>
    </form>
  )
}
