'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'

export default function DebugPage() {
  const [user, setUser] = useState<any>(null)
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      console.log('🔍 Iniciando verificación de autenticación...')

      // Verificar sesión
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      console.log('Sesión:', session)
      console.log('Error de sesión:', sessionError)

      // Verificar usuario
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      console.log('Usuario:', user)
      console.log('Error de usuario:', userError)
      setUser(user)

      if (user) {
        console.log('✅ Usuario encontrado, buscando perfil...')
        // Verificar perfil
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('auth_user_id', user.id)
          .single()

        console.log('Perfil:', profile)
        console.log('Error de perfil:', profileError)
        setProfile(profile)

        if (profile) {
          console.log('✅ Perfil encontrado')
        } else {
          console.log('❌ No se encontró perfil para el usuario')
        }
      } else {
        console.log('❌ No hay usuario autenticado')
      }
    } catch (error) {
      console.error('❌ Error en checkAuth:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    console.log('🚪 Cerrando sesión...')
    await supabase.auth.signOut()
    setUser(null)
    setProfile(null)
    console.log('✅ Sesión cerrada')
  }

  const handleGoogleLogin = async () => {
    try {
      console.log('🔑 Iniciando login con Google...')
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/api/auth/callback`
        }
      })

      if (error) {
        console.error('❌ Error en Google login:', error)
      } else {
        console.log('✅ Redirigiendo a Google...')
      }
    } catch (error) {
      console.error('❌ Error:', error)
    }
  }

  if (loading) {
    return <div className="p-8 text-white bg-drago-black min-h-screen">Cargando...</div>
  }

  return (
    <div className="p-8 text-white bg-drago-black min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Debug de Autenticación</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-800 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Usuario de Supabase Auth:</h2>
          {user ? (
            <pre className="text-sm overflow-auto">
              {JSON.stringify(user, null, 2)}
            </pre>
          ) : (
            <p className="text-red-400">No hay usuario autenticado</p>
          )}
        </div>

        <div className="bg-gray-800 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Perfil en Base de Datos:</h2>
          {profile ? (
            <pre className="text-sm overflow-auto">
              {JSON.stringify(profile, null, 2)}
            </pre>
          ) : (
            <p className="text-red-400">No hay perfil en la base de datos</p>
          )}
        </div>

        <div className="space-x-4 space-y-2">
          <button
            onClick={checkAuth}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            🔄 Recargar
          </button>

          {!user && (
            <button
              onClick={handleGoogleLogin}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              🔑 Login con Google
            </button>
          )}

          {user && (
            <button
              onClick={handleSignOut}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              🚪 Cerrar Sesión
            </button>
          )}

          <a
            href="/auth/login"
            className="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            📝 Ir a Login
          </a>

          <a
            href="/admin"
            className="inline-block px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            ⚙️ Ir a Admin
          </a>
        </div>
      </div>
    </div>
  )
}
