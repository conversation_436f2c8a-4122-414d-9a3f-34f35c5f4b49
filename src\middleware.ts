import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Verificar autenticación para rutas de admin
  if (request.nextUrl.pathname.startsWith('/admin')) {
    console.log('🔍 Middleware: Verificando acceso a admin')

    try {
      const {
        data: { user },
        error: userError
      } = await supabase.auth.getUser()

      console.log('👤 Usuario en middleware:', user?.email, 'ID:', user?.id)
      console.log('❌ Error de usuario:', userError)

      if (!user || userError) {
        console.log('🚫 Sin usuario o error, redirigiendo a login')
        return NextResponse.redirect(new URL('/auth/login', request.url))
      }

      // Verificar si el usuario tiene perfil y está aprobado
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, status, nombres, apellidos')
        .eq('auth_user_id', user.id)
        .single()

      console.log('📋 Perfil encontrado:', profile)
      console.log('❌ Error de perfil:', profileError)

      if (profileError || !profile) {
        console.log('🚫 Sin perfil o error, redirigiendo a completar perfil')
        return NextResponse.redirect(new URL('/auth/complete-profile', request.url))
      }

      if (profile.status !== 'approved') {
        console.log('⏳ Usuario no aprobado, status:', profile.status)
        return NextResponse.redirect(new URL('/?status=pending', request.url))
      }

      if (!['admin', 'moderador', 'periodista', 'booking', 'manager'].includes(profile.role)) {
        console.log('🚫 Rol no válido:', profile.role)
        return NextResponse.redirect(new URL('/?status=unauthorized', request.url))
      }

      console.log('✅ Acceso permitido a admin para:', profile.nombres, profile.apellidos, 'Rol:', profile.role)
    } catch (error) {
      console.error('💥 Error en middleware:', error)
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
