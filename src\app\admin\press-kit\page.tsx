'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { PressKitItem, User } from '@/types'

export default function PressKitAdminPage() {
  const [items, setItems] = useState<PressKitItem[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [filter, setFilter] = useState<'all' | 'photos' | 'biography' | 'press_releases' | 'media' | 'branding' | 'technical'>('all')
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [uploadData, setUploadData] = useState({
    title: '',
    description: '',
    category: 'photos' as PressKitItem['category'],
    file: null as File | null
  })
  const router = useRouter()

  useEffect(() => {
    checkAccess()
    loadPressKitItems()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'manager'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const loadPressKitItems = async () => {
    try {
      const { data, error } = await supabase
        .from('press_kit_files')
        .select(`
          *,
          uploader:profiles!press_kit_files_uploaded_by_fkey(nombres, apellidos)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setItems(data || [])
    } catch (error) {
      console.error('Error loading press kit items:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (itemId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este archivo?')) return

    try {
      const { error } = await supabase
        .from('press_kit_files')
        .delete()
        .eq('id', itemId)

      if (error) throw error
      loadPressKitItems()
    } catch (error) {
      console.error('Error deleting item:', error)
    }
  }

  const uploadFile = async (file: File) => {
    const fileExt = file.name.split('.').pop()
    const fileName = `press-kit/${uploadData.category}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from('press-kit')
      .upload(fileName, file)

    if (error) throw error

    const { data: { publicUrl } } = supabase.storage
      .from('press-kit')
      .getPublicUrl(fileName)

    return publicUrl
  }

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!uploadData.file) return

    setUploading(true)

    try {
      const fileUrl = await uploadFile(uploadData.file)
      
      const fileType = uploadData.file.type.startsWith('image/') ? 'image' :
                      uploadData.file.type === 'application/pdf' ? 'pdf' :
                      uploadData.file.type.startsWith('video/') ? 'video' :
                      uploadData.file.type.startsWith('audio/') ? 'audio' : 'document'

      const { error } = await supabase
        .from('press_kit_files')
        .insert({
          title: uploadData.title,
          description: uploadData.description,
          file_url: fileUrl,
          file_type: fileType,
          file_size: uploadData.file.size,
          category: uploadData.category,
          uploaded_by: currentUser?.id
        })

      if (error) throw error

      setShowUploadForm(false)
      setUploadData({
        title: '',
        description: '',
        category: 'photos',
        file: null
      })
      loadPressKitItems()
    } catch (error) {
      console.error('Error uploading file:', error)
      alert('Error al subir el archivo')
    } finally {
      setUploading(false)
    }
  }

  const filteredItems = items.filter(item => {
    if (filter === 'all') return true
    return item.category === filter
  })

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'photos': return 'bg-blue-600'
      case 'biography': return 'bg-green-600'
      case 'press_releases': return 'bg-purple-600'
      case 'media': return 'bg-orange-600'
      case 'branding': return 'bg-pink-600'
      case 'technical': return 'bg-gray-600'
      default: return 'bg-gray-600'
    }
  }

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'image': return '🖼️'
      case 'pdf': return '📄'
      case 'video': return '🎥'
      case 'audio': return '🎵'
      default: return '📁'
    }
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const categories = [
    { value: 'all', label: 'Todos', count: items.length },
    { value: 'photos', label: 'Fotos', count: items.filter(i => i.category === 'photos').length },
    { value: 'biography', label: 'Biografía', count: items.filter(i => i.category === 'biography').length },
    { value: 'press_releases', label: 'Comunicados', count: items.filter(i => i.category === 'press_releases').length },
    { value: 'media', label: 'Media', count: items.filter(i => i.category === 'media').length },
    { value: 'branding', label: 'Branding', count: items.filter(i => i.category === 'branding').length },
    { value: 'technical', label: 'Técnico', count: items.filter(i => i.category === 'technical').length }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando press kit...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Press Kit</h1>
              <p className="text-gray-400">Gestión de archivos promocionales</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button 
                variant="primary" 
                glow
                onClick={() => setShowUploadForm(true)}
              >
                + Subir Archivo
              </Button>
              <Button variant="ghost" onClick={() => router.push('/admin')}>
                ← Dashboard
              </Button>
            </div>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          {/* Formulario de Subida */}
          {showUploadForm && (
            <Card variant="glass" className="p-6 mb-8">
              <h2 className="text-xl font-bold text-white mb-6">Subir Nuevo Archivo</h2>
              
              <form onSubmit={handleUpload} className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Título *
                    </label>
                    <input
                      type="text"
                      value={uploadData.title}
                      onChange={(e) => setUploadData(prev => ({ ...prev, title: e.target.value }))}
                      required
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      placeholder="Nombre del archivo"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Categoría
                    </label>
                    <select
                      value={uploadData.category}
                      onChange={(e) => setUploadData(prev => ({ ...prev, category: e.target.value as any }))}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    >
                      <option value="photos">Fotos</option>
                      <option value="biography">Biografía</option>
                      <option value="press_releases">Comunicados</option>
                      <option value="media">Media</option>
                      <option value="branding">Branding</option>
                      <option value="technical">Técnico</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Archivo *
                    </label>
                    <input
                      type="file"
                      onChange={(e) => setUploadData(prev => ({ ...prev, file: e.target.files?.[0] || null }))}
                      required
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-drago-red file:text-white hover:file:bg-drago-red-dark"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Descripción
                  </label>
                  <textarea
                    value={uploadData.description}
                    onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))}
                    rows={6}
                    className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    placeholder="Descripción del archivo..."
                  />
                </div>

                <div className="md:col-span-2 flex justify-end space-x-4">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => setShowUploadForm(false)}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={uploading}
                  >
                    {uploading ? 'Subiendo...' : 'Subir Archivo'}
                  </Button>
                </div>
              </form>
            </Card>
          )}

          {/* Filtros */}
          <div className="flex flex-wrap gap-4 mb-8">
            {categories.map(category => (
              <Button
                key={category.value}
                variant={filter === category.value ? 'primary' : 'ghost'}
                onClick={() => setFilter(category.value as any)}
                size="sm"
              >
                {category.label} ({category.count})
              </Button>
            ))}
          </div>

          {/* Grid de Archivos */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItems.map(item => (
              <Card key={item.id} variant="glass" className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getFileTypeIcon(item.file_type)}</span>
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        {item.title}
                      </h3>
                      <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getCategoryColor(item.category)}`}>
                        {categories.find(c => c.value === item.category)?.label}
                      </span>
                    </div>
                  </div>
                </div>

                {item.description && (
                  <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                    {item.description}
                  </p>
                )}

                <div className="space-y-2 text-sm text-gray-400 mb-4">
                  <div>Tamaño: {formatFileSize(item.file_size)}</div>
                  <div>Subido: {new Date(item.created_at).toLocaleDateString('es-CL')}</div>
                  {item.uploader && (
                    <div>Por: {item.uploader.nombres} {item.uploader.apellidos}</div>
                  )}
                </div>

                <div className="flex gap-2">
                  <a
                    href={item.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1"
                  >
                    <Button variant="primary" size="sm" className="w-full">
                      📥 Descargar
                    </Button>
                  </a>
                  
                  {item.file_type === 'image' && (
                    <a
                      href={item.file_url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Button variant="ghost" size="sm">
                        👁️ Ver
                      </Button>
                    </a>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(item.id)}
                    className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                  >
                    🗑️
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <Card variant="glass" className="p-8 text-center">
              <p className="text-gray-400 mb-4">No hay archivos en esta categoría.</p>
              <Button variant="primary" onClick={() => setShowUploadForm(true)}>
                Subir el primer archivo
              </Button>
            </Card>
          )}
        </Container>
      </main>
    </div>
  )
}
