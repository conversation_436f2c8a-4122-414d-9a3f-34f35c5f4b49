'use client'

import { useState } from 'react'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export default function PressKitPage() {
  const [activeCategory, setActiveCategory] = useState('all')

  const pressKitItems = [
    {
      id: '1',
      title: 'Biografía Oficial',
      description: 'Biografía completa de Drago200 en español e inglés',
      type: 'document',
      category: 'biography',
      fileSize: '2.5 MB',
      format: 'PDF',
      downloadUrl: '#'
    },
    {
      id: '2',
      title: 'Fotos Promocionales HD',
      description: 'Pack de fotos profesionales en alta resolución',
      type: 'image',
      category: 'photos',
      fileSize: '45 MB',
      format: 'ZIP',
      downloadUrl: '#'
    },
    {
      id: '3',
      title: 'Logo Oficial',
      description: 'Logo de Drago200 en diferentes formatos y colores',
      type: 'image',
      category: 'branding',
      fileSize: '8 MB',
      format: 'ZIP',
      downloadUrl: '#'
    },
    {
      id: '4',
      title: 'Comunicado de Prensa - Nuevo Álbum',
      description: 'Comunicado oficial sobre el último lanzamiento',
      type: 'document',
      category: 'press',
      fileSize: '1.2 MB',
      format: 'PDF',
      downloadUrl: '#'
    },
    {
      id: '5',
      title: 'Rider Técnico',
      description: 'Especificaciones técnicas para presentaciones en vivo',
      type: 'document',
      category: 'technical',
      fileSize: '3.1 MB',
      format: 'PDF',
      downloadUrl: '#'
    },
    {
      id: '6',
      title: 'Video Promocional',
      description: 'Video promocional oficial de 2 minutos',
      type: 'video',
      category: 'media',
      fileSize: '120 MB',
      format: 'MP4',
      downloadUrl: '#'
    },
    {
      id: '7',
      title: 'Fotos de Conciertos',
      description: 'Galería de fotos de presentaciones en vivo',
      type: 'image',
      category: 'photos',
      fileSize: '67 MB',
      format: 'ZIP',
      downloadUrl: '#'
    },
    {
      id: '8',
      title: 'Fact Sheet',
      description: 'Datos clave y estadísticas de carrera',
      type: 'document',
      category: 'biography',
      fileSize: '800 KB',
      format: 'PDF',
      downloadUrl: '#'
    }
  ]

  const categories = [
    { id: 'all', label: 'Todo', icon: '📁' },
    { id: 'biography', label: 'Biografía', icon: '📖' },
    { id: 'photos', label: 'Fotos', icon: '📷' },
    { id: 'branding', label: 'Branding', icon: '🎨' },
    { id: 'press', label: 'Prensa', icon: '📰' },
    { id: 'technical', label: 'Técnico', icon: '🔧' },
    { id: 'media', label: 'Media', icon: '🎬' }
  ]

  const stats = [
    { label: 'Años de Carrera', value: '20+' },
    { label: 'Álbumes Lanzados', value: '8' },
    { label: 'Canciones Producidas', value: '150+' },
    { label: 'Colaboraciones', value: '50+' },
    { label: 'Países con Presencia', value: '15' },
    { label: 'Streaming Mensual', value: '2M+' }
  ]

  const awards = [
    'Pionero del Reggaetón Chileno (2020)',
    'Mejor Artista Urbano Nacional (2015)',
    'Reconocimiento a la Trayectoria Musical (2018)',
    'Embajador de la Música Urbana Chilena (2019)',
    'Premio a la Innovación Musical (2021)'
  ]

  const filteredItems = pressKitItems.filter(item => 
    activeCategory === 'all' || item.category === activeCategory
  )

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'document': return '📄'
      case 'image': return '🖼️'
      case 'video': return '🎥'
      default: return '📁'
    }
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-red-black">
        <Container>
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Press Kit
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Recursos oficiales para medios de comunicación, promotores y profesionales de la industria. 
              Todo lo que necesitas sobre Drago200 en un solo lugar.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" glow>
                📥 Descargar Pack Completo
              </Button>
              <Button variant="outline" size="lg">
                📧 Contactar Prensa
              </Button>
            </div>
          </div>
        </Container>
      </section>

      {/* Quick Stats */}
      <section className="py-16 bg-drago-black-light">
        <Container>
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Datos Clave</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} variant="glass" className="p-6 text-center">
                <div className="text-3xl font-bold text-drago-red mb-2">{stat.value}</div>
                <div className="text-gray-300 text-sm">{stat.label}</div>
              </Card>
            ))}
          </div>
        </Container>
      </section>

      {/* Categories Filter */}
      <section className="py-8 bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={activeCategory === category.id ? 'primary' : 'ghost'}
                onClick={() => setActiveCategory(category.id)}
                size="sm"
                className="min-w-[120px]"
              >
                {category.icon} {category.label}
              </Button>
            ))}
          </div>
        </Container>
      </section>

      {/* Press Kit Items */}
      <section className="section-padding">
        <Container>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItems.map((item) => (
              <Card key={item.id} hover variant="glass" className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="text-3xl">{getTypeIcon(item.type)}</div>
                  <div className="text-right">
                    <div className="text-xs text-gray-400">{item.format}</div>
                    <div className="text-xs text-gray-400">{item.fileSize}</div>
                  </div>
                </div>
                
                <h3 className="text-lg font-bold text-white mb-2">{item.title}</h3>
                <p className="text-gray-300 text-sm mb-4 line-clamp-2">{item.description}</p>
                
                <div className="flex gap-2">
                  <Button variant="primary" size="sm" className="flex-1">
                    📥 Descargar
                  </Button>
                  <Button variant="ghost" size="sm">
                    👁️
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </Container>
      </section>

      {/* Biography Summary */}
      <section className="section-padding bg-drago-black-light">
        <Container>
          <div className="grid lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">Biografía Resumida</h2>
              <Card variant="glass" className="p-8">
                <p className="text-gray-300 mb-4 leading-relaxed">
                  <strong className="text-white">Drago200</strong> es reconocido como el pionero del reggaetón en Chile, 
                  con más de 20 años de carrera musical que han marcado el desarrollo del género urbano en el país.
                </p>
                <p className="text-gray-300 mb-4 leading-relaxed">
                  Desde sus inicios a finales de los años 90, ha sido una figura clave en la evolución de la música 
                  urbana chilena, influenciando a múltiples generaciones de artistas y estableciendo las bases 
                  del reggaetón nacional.
                </p>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Su legado incluye más de 150 canciones producidas, 8 álbumes lanzados y colaboraciones con 
                  artistas tanto nacionales como internacionales, consolidándolo como una leyenda viviente 
                  de la música urbana latinoamericana.
                </p>
                <Button variant="outline">
                  📖 Biografía Completa
                </Button>
              </Card>
            </div>
            
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">Premios y Reconocimientos</h2>
              <div className="space-y-4">
                {awards.map((award, index) => (
                  <Card key={index} variant="glass" className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-drago-red rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-sm">🏆</span>
                      </div>
                      <p className="text-white font-medium">{award}</p>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Contact for Press */}
      <section className="section-padding">
        <Container>
          <Card variant="gradient" className="p-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              ¿Necesitas Más Información?
            </h2>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Para entrevistas, material adicional o consultas específicas de prensa, 
              contacta directamente con nuestro equipo de comunicaciones.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary">
                📧 <EMAIL>
              </Button>
              <Button size="lg" variant="ghost">
                📞 +56 9 XXXX XXXX
              </Button>
            </div>
          </Card>
        </Container>
      </section>
    </div>
  )
}
