'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { Event, User } from '@/types'

interface EventFormData {
  title: string
  description: string
  event_date: string
  venue: string
  city: string
  country: string
  ticket_url: string
  price_range: string
  capacity: number
  event_type: 'concierto' | 'festival' | 'colaboracion' | 'entrevista' | 'otro'
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
}

export default function NewEventPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [posterFile, setPosterFile] = useState<File | null>(null)
  const [posterPreview, setPosterPreview] = useState<string>('')
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    event_date: '',
    venue: '',
    city: '',
    country: 'Chile',
    ticket_url: '',
    price_range: '',
    capacity: 0,
    event_type: 'concierto',
    status: 'upcoming'
  })
  const router = useRouter()

  useEffect(() => {
    checkAccess()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'booking'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  const handlePosterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setPosterFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setPosterPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const uploadPoster = async (file: File) => {
    const fileExt = file.name.split('.').pop()
    const fileName = `events/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from('press-kit')
      .upload(fileName, file)

    if (error) throw error

    const { data: { publicUrl } } = supabase.storage
      .from('press-kit')
      .getPublicUrl(fileName)

    return publicUrl
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let posterUrl = ''

      // Subir poster si existe
      if (posterFile) {
        setUploading(true)
        posterUrl = await uploadPoster(posterFile)
      }

      // Crear el evento en la base de datos
      const eventData = {
        ...formData,
        poster_url: posterUrl || null,
        created_by: currentUser?.id
      }

      const { error } = await supabase
        .from('events')
        .insert(eventData)

      if (error) throw error

      router.push('/admin/events')
    } catch (error) {
      console.error('Error creating event:', error)
      alert('Error al crear el evento')
    } finally {
      setLoading(false)
      setUploading(false)
    }
  }

  const eventTypes = [
    { value: 'concierto', label: 'Concierto' },
    { value: 'festival', label: 'Festival' },
    { value: 'colaboracion', label: 'Colaboración' },
    { value: 'entrevista', label: 'Entrevista' },
    { value: 'otro', label: 'Otro' }
  ]

  const countries = [
    'Chile', 'Argentina', 'Perú', 'Colombia', 'México', 'España', 'Estados Unidos', 'Otro'
  ]

  const formatDateForInput = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day}T${hours}:${minutes}`
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Nuevo Evento</h1>
              <p className="text-gray-400">Crear un nuevo evento o concierto</p>
            </div>
            <Button variant="ghost" onClick={() => router.push('/admin/events')}>
              ← Volver a Eventos
            </Button>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Información Básica */}
              <Card variant="glass" className="p-6">
                <h2 className="text-xl font-bold text-white mb-6">Información del Evento</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Título del Evento *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      placeholder="Nombre del evento"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Tipo de Evento
                    </label>
                    <select
                      name="event_type"
                      value={formData.event_type}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    >
                      {eventTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Fecha y Hora *
                    </label>
                    <input
                      type="datetime-local"
                      name="event_date"
                      value={formData.event_date}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Venue/Local
                    </label>
                    <input
                      type="text"
                      name="venue"
                      value={formData.venue}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      placeholder="Nombre del lugar"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Ciudad
                      </label>
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                        placeholder="Santiago"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        País
                      </label>
                      <select
                        name="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      >
                        {countries.map(country => (
                          <option key={country} value={country}>
                            {country}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Descripción
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      placeholder="Descripción del evento..."
                    />
                  </div>
                </div>
              </Card>

              {/* Información Comercial */}
              <Card variant="glass" className="p-6">
                <h2 className="text-xl font-bold text-white mb-6">Información Comercial</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Capacidad
                    </label>
                    <input
                      type="number"
                      name="capacity"
                      value={formData.capacity}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      placeholder="1000"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Rango de Precios
                    </label>
                    <input
                      type="text"
                      name="price_range"
                      value={formData.price_range}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      placeholder="$15.000 - $50.000"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      URL de Tickets
                    </label>
                    <input
                      type="url"
                      name="ticket_url"
                      value={formData.ticket_url}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      placeholder="https://tickets.com/evento"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Estado
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    >
                      <option value="upcoming">Próximo</option>
                      <option value="ongoing">En curso</option>
                      <option value="completed">Completado</option>
                      <option value="cancelled">Cancelado</option>
                    </select>
                  </div>

                  {/* Poster */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Poster del Evento
                    </label>
                    <input
                      type="file"
                      accept="image/jpeg,image/png,image/webp"
                      onChange={handlePosterChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-drago-red file:text-white hover:file:bg-drago-red-dark"
                    />
                    
                    {posterPreview && (
                      <div className="mt-4">
                        <img
                          src={posterPreview}
                          alt="Preview"
                          className="w-full h-48 object-cover rounded-lg"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </div>

            {/* Preview del Evento */}
            {formData.title && (
              <Card variant="glass" className="p-6 mt-8">
                <h2 className="text-xl font-bold text-white mb-4">Preview del Evento</h2>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">
                      {formData.title}
                    </h3>
                    
                    <div className="space-y-2 text-sm text-gray-300">
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs font-semibold text-white bg-purple-600`}>
                          {eventTypes.find(t => t.value === formData.event_type)?.label}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-semibold text-white bg-blue-600`}>
                          {formData.status}
                        </span>
                      </div>
                      
                      {formData.event_date && (
                        <div>📅 {new Date(formData.event_date).toLocaleDateString('es-CL', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}</div>
                      )}
                      
                      {formData.venue && (
                        <div>📍 {formData.venue}</div>
                      )}
                      
                      {formData.city && (
                        <div>🌍 {formData.city}, {formData.country}</div>
                      )}
                      
                      {formData.capacity > 0 && (
                        <div>👥 Capacidad: {formData.capacity.toLocaleString()}</div>
                      )}
                      
                      {formData.price_range && (
                        <div>💰 {formData.price_range}</div>
                      )}
                    </div>
                    
                    {formData.description && (
                      <p className="text-gray-300 text-sm mt-3">
                        {formData.description}
                      </p>
                    )}
                  </div>
                  
                  {posterPreview && (
                    <div>
                      <img
                        src={posterPreview}
                        alt="Poster preview"
                        className="w-full h-64 object-cover rounded-lg"
                      />
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* Botones */}
            <div className="flex justify-end space-x-4 mt-8">
              <Button
                type="button"
                variant="ghost"
                onClick={() => router.push('/admin/events')}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={loading || uploading}
                glow
              >
                {uploading ? 'Subiendo poster...' : loading ? 'Guardando...' : 'Crear Evento'}
              </Button>
            </div>
          </form>
        </Container>
      </main>
    </div>
  )
}
