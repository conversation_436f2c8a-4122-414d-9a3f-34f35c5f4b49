import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/'

  if (code) {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.delete({ name, ...options })
          },
        },
      }
    )

    const { error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (!error) {
      // Verificar si el usuario tiene un perfil
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        // Verificar si existe el perfil
        const { data: existingProfile } = await supabase
          .from('profiles')
          .select('id')
          .eq('auth_user_id', user.id)
          .single()

        // Si no existe el perfil, crearlo
        if (!existingProfile) {
          await supabase
            .from('profiles')
            .insert({
              auth_user_id: user.id,
              email: user.email!,
              nombres: user.user_metadata?.full_name || user.user_metadata?.name || 'Usuario',
              apellidos: '',
              avatar_url: user.user_metadata?.avatar_url || null,
              role: 'pending', // Por defecto, los nuevos usuarios están pendientes
              status: 'pending'
            })
        }

        // Verificar si es admin para redirigir apropiadamente
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('auth_user_id', user.id)
          .single()

        if (profile?.role === 'admin') {
          return NextResponse.redirect(`${origin}/admin`)
        }
      }

      return NextResponse.redirect(`${origin}${next}`)
    }
  }

  // Si hay error, redirigir al login con mensaje de error
  return NextResponse.redirect(`${origin}/auth/login?error=auth_callback_error`)
}
