export interface User {
  id: string
  auth_user_id: string
  email: string
  nombres: string
  apellidos: string
  fecha_nacimiento?: string
  pais?: string
  edad?: number
  role: 'admin' | 'moderador' | 'periodista' | 'booking' | 'manager' | 'pending'
  status: 'pending' | 'approved' | 'rejected'
  avatar_url?: string
  created_at: string
  updated_at: string
  approved_by?: string
  approved_at?: string
}

export interface Song {
  id: string
  title: string
  artist: string
  album?: string
  duration?: number
  audio_url?: string
  cover_url?: string
  release_date?: string
  genre?: string
  featured: boolean
  spotify_streams?: number
  youtube_views?: number
  apple_music_streams?: number
  collaborators?: string[]
  lyrics?: string
  description?: string
  status: 'draft' | 'published' | 'archived'
  created_by?: string
  created_at: string
  updated_at: string
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featured_image?: string
  category: 'noticias' | 'eventos' | 'musica' | 'entrevistas' | 'colaboraciones'
  tags?: string[]
  published: boolean
  featured: boolean
  author_id: string
  author?: User
  created_at: string
  updated_at: string
  published_at?: string
}

export interface PressKitItem {
  id: string
  title: string
  description?: string
  file_url: string
  file_type: 'image' | 'pdf' | 'video' | 'audio'
  category: 'photos' | 'biography' | 'press_releases' | 'media'
  created_at: string
  updated_at: string
}

export interface ContactMessage {
  id: string
  name: string
  email: string
  subject: string
  message: string
  type: 'general' | 'booking' | 'press' | 'collaboration' | 'business'
  status: 'new' | 'read' | 'replied' | 'archived'
  replied_by?: string
  reply_message?: string
  created_at: string
  updated_at: string
}

export interface Event {
  id: string
  title: string
  description?: string
  event_date: string
  venue?: string
  city?: string
  country?: string
  ticket_url?: string
  poster_url?: string
  price_range?: string
  capacity?: number
  event_type: 'concierto' | 'festival' | 'colaboracion' | 'entrevista' | 'otro'
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
  created_by?: string
  created_at: string
  updated_at: string
}

export interface Album {
  id: string
  title: string
  description?: string
  release_date?: string
  cover_url?: string
  album_type: 'album' | 'ep' | 'single' | 'compilation'
  total_tracks: number
  total_duration: number
  spotify_url?: string
  apple_music_url?: string
  youtube_url?: string
  status: 'draft' | 'published' | 'archived'
  created_by?: string
  created_at: string
  updated_at: string
  songs?: Song[]
}

export interface UserRegistrationForm {
  nombres: string
  apellidos: string
  fecha_nacimiento: string
  pais: string
  edad: number
  email: string
  mensaje?: string
}

// Tipos para componentes
export interface NavItem {
  name: string
  href: string
  icon?: React.ComponentType<any>
}

export interface SocialLink {
  name: string
  url: string
  icon: React.ComponentType<any>
}

// Tipos para el reproductor de música
export interface PlayerState {
  isPlaying: boolean
  currentSong?: Song
  playlist: Song[]
  currentIndex: number
  volume: number
  duration: number
  currentTime: number
  isLoading: boolean
}

// Tipos para el tema/diseño
export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: string
  textSecondary: string
}

export interface Biography {
  id: string
  content: string
  achievements: string[]
  timeline: TimelineEvent[]
  updated_at: string
}

export interface TimelineEvent {
  id: string
  year: number
  title: string
  description: string
  image_url?: string
}

export interface Award {
  id: string
  title: string
  organization: string
  year: number
  description?: string
  image_url?: string
}
