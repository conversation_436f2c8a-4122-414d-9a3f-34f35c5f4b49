-- Esquema de base de datos para Drago200 Website
-- Ejecutar estas consultas en el SQL Editor de Supabase

-- 1. Tabla de perfiles de usuarios
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  nombres VARCHAR(100) NOT NULL,
  apellidos VARCHAR(100) NOT NULL,
  fecha_nacimiento DATE,
  pais VARCHAR(50),
  edad INTEGER,
  role VARCHAR(20) DEFAULT 'pending' CHECK (role IN ('admin', 'moderador', 'periodista', 'booking', 'manager', 'pending')),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  approved_by UUID REFERENCES profiles(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(auth_user_id),
  UNIQUE(email)
);

-- 2. Tabla de canciones
CREATE TABLE IF NOT EXISTS songs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  artist VARCHAR(255) DEFAULT 'Drago200',
  album VARCHAR(255),
  duration INTEGER, -- en segundos
  audio_url TEXT,
  cover_url TEXT,
  release_date DATE,
  genre VARCHAR(50),
  featured BOOLEAN DEFAULT FALSE,
  spotify_streams BIGINT DEFAULT 0,
  youtube_views BIGINT DEFAULT 0,
  apple_music_streams BIGINT DEFAULT 0,
  collaborators TEXT[], -- Array de colaboradores
  lyrics TEXT,
  description TEXT,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Tabla de posts del blog
CREATE TABLE IF NOT EXISTS blog_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT,
  featured_image TEXT,
  category VARCHAR(50) DEFAULT 'noticias' CHECK (category IN ('noticias', 'eventos', 'musica', 'entrevistas', 'colaboraciones')),
  tags TEXT[],
  published BOOLEAN DEFAULT FALSE,
  featured BOOLEAN DEFAULT FALSE,
  author_id UUID REFERENCES profiles(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE
);

-- 4. Tabla de eventos
CREATE TABLE IF NOT EXISTS events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  event_date TIMESTAMP WITH TIME ZONE NOT NULL,
  venue VARCHAR(255),
  city VARCHAR(100),
  country VARCHAR(50),
  ticket_url TEXT,
  poster_url TEXT,
  price_range VARCHAR(100),
  capacity INTEGER,
  event_type VARCHAR(50) DEFAULT 'concierto' CHECK (event_type IN ('concierto', 'festival', 'colaboracion', 'entrevista', 'otro')),
  status VARCHAR(20) DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'ongoing', 'completed', 'cancelled')),
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Tabla de mensajes de contacto
CREATE TABLE IF NOT EXISTS contact_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(50) DEFAULT 'general' CHECK (type IN ('general', 'booking', 'press', 'collaboration', 'business')),
  status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'read', 'replied', 'archived')),
  replied_by UUID REFERENCES profiles(id),
  reply_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Tabla de archivos del press kit
CREATE TABLE IF NOT EXISTS press_kit_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  file_url TEXT NOT NULL,
  file_type VARCHAR(20) CHECK (file_type IN ('image', 'pdf', 'video', 'audio', 'document')),
  file_size BIGINT,
  category VARCHAR(50) CHECK (category IN ('photos', 'biography', 'press_releases', 'media', 'branding', 'technical')),
  uploaded_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Tabla de álbumes
CREATE TABLE IF NOT EXISTS albums (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  release_date DATE,
  cover_url TEXT,
  album_type VARCHAR(50) DEFAULT 'album' CHECK (album_type IN ('album', 'ep', 'single', 'compilation')),
  total_tracks INTEGER DEFAULT 0,
  total_duration INTEGER DEFAULT 0, -- en segundos
  spotify_url TEXT,
  apple_music_url TEXT,
  youtube_url TEXT,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Tabla de relación álbum-canciones
CREATE TABLE IF NOT EXISTS album_songs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  album_id UUID REFERENCES albums(id) ON DELETE CASCADE,
  song_id UUID REFERENCES songs(id) ON DELETE CASCADE,
  track_number INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(album_id, song_id),
  UNIQUE(album_id, track_number)
);

-- Índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_profiles_auth_user_id ON profiles(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_songs_status ON songs(status);
CREATE INDEX IF NOT EXISTS idx_songs_featured ON songs(featured);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts(published);
CREATE INDEX IF NOT EXISTS idx_blog_posts_author ON blog_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_blog_posts_category ON blog_posts(category);
CREATE INDEX IF NOT EXISTS idx_events_status ON events(status);
CREATE INDEX IF NOT EXISTS idx_events_date ON events(event_date);
CREATE INDEX IF NOT EXISTS idx_contact_messages_status ON contact_messages(status);

-- Triggers para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_songs_updated_at BEFORE UPDATE ON songs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON blog_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_events_updated_at BEFORE UPDATE ON events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contact_messages_updated_at BEFORE UPDATE ON contact_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_press_kit_files_updated_at BEFORE UPDATE ON press_kit_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_albums_updated_at BEFORE UPDATE ON albums FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Políticas de seguridad RLS (Row Level Security)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE songs ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE press_kit_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE albums ENABLE ROW LEVEL SECURITY;
ALTER TABLE album_songs ENABLE ROW LEVEL SECURITY;

-- Políticas para la tabla profiles
CREATE POLICY "Los usuarios pueden ver su propio perfil" ON profiles FOR SELECT USING (auth_user_id = auth.uid());
CREATE POLICY "Los usuarios pueden actualizar su propio perfil" ON profiles FOR UPDATE USING (auth_user_id = auth.uid());
CREATE POLICY "Los admins pueden ver todos los perfiles" ON profiles FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE auth_user_id = auth.uid() 
    AND role = 'admin' 
    AND status = 'approved'
  )
);
CREATE POLICY "Los admins pueden actualizar todos los perfiles" ON profiles FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE auth_user_id = auth.uid() 
    AND role = 'admin' 
    AND status = 'approved'
  )
);

-- Políticas para contenido público (lectura)
CREATE POLICY "Contenido público visible para todos" ON songs FOR SELECT USING (status = 'published');
CREATE POLICY "Posts publicados visibles para todos" ON blog_posts FOR SELECT USING (published = true);
CREATE POLICY "Eventos públicos visibles para todos" ON events FOR SELECT USING (status IN ('upcoming', 'ongoing'));

-- Políticas para creación y edición de contenido (solo usuarios autorizados)
CREATE POLICY "Managers pueden gestionar canciones" ON songs FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE auth_user_id = auth.uid() 
    AND role IN ('admin', 'manager') 
    AND status = 'approved'
  )
);

CREATE POLICY "Periodistas pueden gestionar blog posts" ON blog_posts FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE auth_user_id = auth.uid() 
    AND role IN ('admin', 'periodista', 'moderador') 
    AND status = 'approved'
  )
);

CREATE POLICY "Booking puede gestionar eventos" ON events FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE auth_user_id = auth.uid() 
    AND role IN ('admin', 'booking') 
    AND status = 'approved'
  )
);

-- Insertar usuario admin inicial (reemplazar con tu email)
-- INSERT INTO profiles (auth_user_id, email, nombres, apellidos, role, status, approved_at)
-- VALUES (
--   (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1),
--   '<EMAIL>',
--   'Admin',
--   'Principal',
--   'admin',
--   'approved',
--   NOW()
-- );
