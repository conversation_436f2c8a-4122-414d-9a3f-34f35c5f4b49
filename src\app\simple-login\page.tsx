'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

export default function SimpleLoginPage() {
  const [loading, setLoading] = useState(false)
  const [status, setStatus] = useState('')
  const router = useRouter()

  const handleGoogleLogin = async () => {
    try {
      setLoading(true)
      setStatus('🔄 Iniciando login con Google...')

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/api/auth/callback`
        }
      })

      if (error) {
        setStatus('❌ Error: ' + error.message)
        console.error('Google login error:', error)
      } else {
        setStatus('✅ Redirigiendo a Google...')
        console.log('Google login success:', data)
      }
    } catch (error) {
      setStatus('❌ Error inesperado: ' + error.message)
      console.error('Unexpected error:', error)
    } finally {
      setLoading(false)
    }
  }

  const checkCurrentUser = async () => {
    try {
      setStatus('🔍 Verificando usuario actual...')
      
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error) {
        setStatus('❌ Error al obtener usuario: ' + error.message)
        return
      }

      if (user) {
        setStatus('✅ Usuario encontrado: ' + user.email)
        console.log('Current user:', user)
        
        // Verificar perfil
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('auth_user_id', user.id)
          .single()

        if (profileError) {
          setStatus('❌ Error al obtener perfil: ' + profileError.message)
          return
        }

        if (profile) {
          setStatus(`✅ Perfil encontrado: ${profile.nombres} ${profile.apellidos} (${profile.role})`)
          console.log('Profile:', profile)
          
          if (profile.role === 'admin' && profile.status === 'approved') {
            setStatus('✅ Acceso admin confirmado. Redirigiendo...')
            setTimeout(() => {
              router.push('/admin')
            }, 1000)
          } else {
            setStatus(`❌ Sin acceso admin. Role: ${profile.role}, Status: ${profile.status}`)
          }
        } else {
          setStatus('❌ No se encontró perfil para el usuario')
        }
      } else {
        setStatus('❌ No hay usuario autenticado')
      }
    } catch (error) {
      setStatus('❌ Error inesperado: ' + error.message)
      console.error('Check user error:', error)
    }
  }

  const forceLogout = async () => {
    try {
      setStatus('🚪 Cerrando sesión...')
      await supabase.auth.signOut()
      setStatus('✅ Sesión cerrada')
    } catch (error) {
      setStatus('❌ Error al cerrar sesión: ' + error.message)
    }
  }

  return (
    <div className="min-h-screen bg-drago-black flex items-center justify-center">
      <div className="max-w-md w-full p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">🔑 Login Simple</h1>
          <p className="text-gray-400">Testing de autenticación</p>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg space-y-4">
          <button
            onClick={handleGoogleLogin}
            disabled={loading}
            className="w-full px-4 py-3 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
          >
            {loading ? '⏳ Cargando...' : '🔑 Login con Google'}
          </button>

          <button
            onClick={checkCurrentUser}
            className="w-full px-4 py-3 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            🔍 Verificar Usuario Actual
          </button>

          <button
            onClick={forceLogout}
            className="w-full px-4 py-3 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            🚪 Logout
          </button>

          {status && (
            <div className="mt-4 p-3 bg-gray-700 rounded text-sm text-gray-300">
              {status}
            </div>
          )}
        </div>

        <div className="mt-6 text-center space-x-4">
          <a
            href="/debug"
            className="text-blue-400 hover:text-blue-300"
          >
            🔍 Debug
          </a>
          <a
            href="/admin"
            className="text-purple-400 hover:text-purple-300"
          >
            ⚙️ Admin
          </a>
          <a
            href="/"
            className="text-green-400 hover:text-green-300"
          >
            🏠 Home
          </a>
        </div>
      </div>
    </div>
  )
}
