'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { BlogPost, User } from '@/types'

export default function BlogAdminPage() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'published' | 'draft'>('all')
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const router = useRouter()

  useEffect(() => {
    checkAccess()
    loadPosts()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'periodista', 'moderador'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const loadPosts = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          author:profiles!blog_posts_author_id_fkey(nombres, apellidos, role)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setPosts(data || [])
    } catch (error) {
      console.error('Error loading posts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePost = async (postId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este post?')) return

    try {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', postId)

      if (error) throw error
      loadPosts()
    } catch (error) {
      console.error('Error deleting post:', error)
    }
  }

  const handleTogglePublish = async (postId: string, currentStatus: boolean) => {
    try {
      const updateData: any = {
        published: !currentStatus
      }

      if (!currentStatus) {
        updateData.published_at = new Date().toISOString()
      } else {
        updateData.published_at = null
      }

      const { error } = await supabase
        .from('blog_posts')
        .update(updateData)
        .eq('id', postId)

      if (error) throw error
      loadPosts()
    } catch (error) {
      console.error('Error updating post:', error)
    }
  }

  const filteredPosts = posts.filter(post => {
    if (filter === 'all') return true
    if (filter === 'published') return post.published
    if (filter === 'draft') return !post.published
    return true
  })

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'noticias': return 'bg-blue-600'
      case 'eventos': return 'bg-purple-600'
      case 'musica': return 'bg-green-600'
      case 'entrevistas': return 'bg-orange-600'
      case 'colaboraciones': return 'bg-pink-600'
      default: return 'bg-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando posts...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Gestión del Blog</h1>
              <p className="text-gray-400">Administra artículos y noticias</p>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/blog/nuevo">
                <Button variant="primary" glow>
                  + Nuevo Post
                </Button>
              </Link>
              <Button variant="ghost" onClick={() => router.push('/admin')}>
                ← Dashboard
              </Button>
            </div>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          {/* Filtros */}
          <div className="flex flex-wrap gap-4 mb-8">
            {[
              { key: 'all', label: 'Todos', count: posts.length },
              { key: 'published', label: 'Publicados', count: posts.filter(p => p.published).length },
              { key: 'draft', label: 'Borradores', count: posts.filter(p => !p.published).length }
            ].map(filterOption => (
              <Button
                key={filterOption.key}
                variant={filter === filterOption.key ? 'primary' : 'ghost'}
                onClick={() => setFilter(filterOption.key as any)}
                size="sm"
              >
                {filterOption.label} ({filterOption.count})
              </Button>
            ))}
          </div>

          {/* Lista de Posts */}
          <div className="space-y-4">
            {filteredPosts.map(post => (
              <Card key={post.id} variant="glass" className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">
                        {post.title}
                      </h3>
                      <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getCategoryColor(post.category)}`}>
                        {post.category}
                      </span>
                      {post.featured && (
                        <span className="px-2 py-1 rounded text-xs font-semibold text-white bg-drago-red">
                          Destacado
                        </span>
                      )}
                      <span className={`text-sm font-medium ${post.published ? 'text-green-400' : 'text-yellow-400'}`}>
                        {post.published ? 'Publicado' : 'Borrador'}
                      </span>
                    </div>
                    
                    <p className="text-gray-300 mb-3 line-clamp-2">
                      {post.excerpt || post.content.substring(0, 150) + '...'}
                    </p>

                    <div className="grid md:grid-cols-3 gap-4 text-sm text-gray-300">
                      <div>
                        <span className="text-gray-400">Autor:</span> {post.author?.nombres} {post.author?.apellidos}
                      </div>
                      <div>
                        <span className="text-gray-400">Creado:</span> {new Date(post.created_at).toLocaleDateString('es-CL')}
                      </div>
                      <div>
                        <span className="text-gray-400">Actualizado:</span> {new Date(post.updated_at).toLocaleDateString('es-CL')}
                      </div>
                    </div>

                    {post.tags && post.tags.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {post.tags.map((tag, index) => (
                            <span key={index} className="px-2 py-1 bg-drago-black-light text-gray-300 rounded text-xs">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2">
                    <Link href={`/admin/blog/editar/${post.id}`}>
                      <Button variant="ghost" size="sm">
                        ✏️ Editar
                      </Button>
                    </Link>
                    
                    <Button
                      variant={post.published ? "outline" : "primary"}
                      size="sm"
                      onClick={() => handleTogglePublish(post.id, post.published)}
                    >
                      {post.published ? '📝 Despublicar' : '🚀 Publicar'}
                    </Button>

                    {post.published && (
                      <Link href={`/blog/${post.slug}`} target="_blank">
                        <Button variant="ghost" size="sm">
                          👁️ Ver
                        </Button>
                      </Link>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeletePost(post.id)}
                      className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                    >
                      🗑️ Eliminar
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {filteredPosts.length === 0 && (
            <Card variant="glass" className="p-8 text-center">
              <p className="text-gray-400 mb-4">No hay posts que coincidan con el filtro seleccionado.</p>
              <Link href="/admin/blog/nuevo">
                <Button variant="primary">
                  Crear el primer post
                </Button>
              </Link>
            </Card>
          )}
        </Container>
      </main>
    </div>
  )
}
