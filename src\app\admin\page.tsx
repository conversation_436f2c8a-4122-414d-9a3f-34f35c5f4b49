'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'

export default function AdminDashboard() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalSongs: 0,
    totalPosts: 0,
    totalMessages: 0,
    monthlyStreams: 0
  })
  const router = useRouter()

  useEffect(() => {
    checkUser()
    loadStats()
  }, [])

  const checkUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/auth/login')
        return
      }

      // Verificar si el usuario es admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (!profile || profile.role !== 'admin') {
        router.push('/')
        return
      }

      setUser(user)
    } catch (error) {
      console.error('Error checking user:', error)
      router.push('/auth/login')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Cargar estadísticas desde Supabase
      const [songsResult, postsResult, messagesResult] = await Promise.all([
        supabase.from('songs').select('id', { count: 'exact' }),
        supabase.from('blog_posts').select('id', { count: 'exact' }),
        supabase.from('contact_messages').select('id', { count: 'exact' })
      ])

      setStats({
        totalSongs: songsResult.count || 0,
        totalPosts: postsResult.count || 0,
        totalMessages: messagesResult.count || 0,
        monthlyStreams: 2500000 // Esto vendría de una API externa
      })
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const quickActions = [
    {
      title: 'Gestionar Usuarios',
      description: 'Aprobar registros y asignar roles',
      icon: '👥',
      href: '/admin/usuarios',
      color: 'bg-red-600'
    },
    {
      title: 'Nuevo Post',
      description: 'Crear un nuevo artículo para el blog',
      icon: '📝',
      href: '/admin/blog/nuevo',
      color: 'bg-green-600'
    },
    {
      title: 'Nueva Canción',
      description: 'Agregar una nueva canción al catálogo',
      icon: '🎵',
      href: '/admin/songs/new',
      color: 'bg-blue-600'
    },
    {
      title: 'Ver Mensajes',
      description: 'Revisar mensajes de contacto',
      icon: '📧',
      href: '/admin/messages',
      color: 'bg-purple-600'
    }
  ]

  const recentActivity = [
    { action: 'Nueva canción agregada', item: 'Reggaetón Chileno 2024', time: '2 horas' },
    { action: 'Post publicado', item: 'Historia del Reggaetón', time: '1 día' },
    { action: 'Mensaje recibido', item: 'Consulta de booking', time: '2 días' },
    { action: 'Press kit actualizado', item: 'Fotos promocionales', time: '3 días' }
  ]

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Panel de Administración</h1>
              <p className="text-gray-400">Bienvenido, {user.email}</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={() => router.push('/')}>
                Ver Sitio Web
              </Button>
              <Button variant="outline" onClick={handleSignOut}>
                Cerrar Sesión
              </Button>
            </div>
          </div>
        </Container>
      </header>

      {/* Dashboard Content */}
      <main className="section-padding">
        <Container>
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Canciones</p>
                  <p className="text-3xl font-bold text-white">{stats.totalSongs}</p>
                </div>
                <div className="text-3xl">🎵</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Posts del Blog</p>
                  <p className="text-3xl font-bold text-white">{stats.totalPosts}</p>
                </div>
                <div className="text-3xl">📝</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Mensajes</p>
                  <p className="text-3xl font-bold text-white">{stats.totalMessages}</p>
                </div>
                <div className="text-3xl">📧</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Streams Mensuales</p>
                  <p className="text-3xl font-bold text-white">{stats.monthlyStreams.toLocaleString()}</p>
                </div>
                <div className="text-3xl">📊</div>
              </div>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Quick Actions */}
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">Acciones Rápidas</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <Card key={index} hover variant="glass" className="p-6 cursor-pointer" onClick={() => router.push(action.href)}>
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center text-2xl`}>
                        {action.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">{action.title}</h3>
                        <p className="text-gray-400 text-sm">{action.description}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">Actividad Reciente</h2>
              <Card variant="glass" className="p-6">
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
                      <div>
                        <p className="text-white font-medium">{activity.action}</p>
                        <p className="text-gray-400 text-sm">{activity.item}</p>
                      </div>
                      <div className="text-gray-400 text-sm">
                        hace {activity.time}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </div>

          {/* Management Sections */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-white mb-6">Gestión de Contenido</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Usuarios', icon: '👥', href: '/admin/usuarios', description: 'Gestionar registros y roles' },
                { title: 'Blog', icon: '📝', href: '/admin/blog', description: 'Administrar posts y noticias' },
                { title: 'Música', icon: '🎵', href: '/admin/songs', description: 'Gestionar canciones y álbumes' },
                { title: 'Mensajes', icon: '📧', href: '/admin/messages', description: 'Ver mensajes de contacto' }
              ].map((section, index) => (
                <Card key={index} hover variant="glass" className="p-6 text-center cursor-pointer" onClick={() => router.push(section.href)}>
                  <div className="text-4xl mb-4">{section.icon}</div>
                  <h3 className="text-lg font-semibold text-white mb-2">{section.title}</h3>
                  <p className="text-gray-400 text-sm">{section.description}</p>
                </Card>
              ))}
            </div>
          </div>
        </Container>
      </main>
    </div>
  )
}
