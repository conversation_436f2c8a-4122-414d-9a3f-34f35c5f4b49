'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'

export default function AdminDashboard() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalSongs: 0,
    totalPosts: 0,
    totalMessages: 0,
    totalEvents: 0,
    monthlyStreams: 0
  })
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      console.log('🔍 Checking user access...')
      const { data: { user }, error: userError } = await supabase.auth.getUser()

      console.log('User:', user)
      console.log('User error:', userError)

      if (!user || userError) {
        console.log('No user found, redirecting to login')
        router.push('/auth/login')
        return
      }

      // Verificar si el usuario es admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, status, nombres, apellidos')
        .eq('auth_user_id', user.id)
        .single()

      console.log('Profile data:', profile)
      console.log('Profile error:', profileError)

      if (!profile) {
        console.log('❌ No profile found for user')
        router.push('/')
        return
      }

      if (profile.role !== 'admin') {
        console.log('❌ User is not admin. Role:', profile.role)
        router.push('/')
        return
      }

      if (profile.status !== 'approved') {
        console.log('❌ User is not approved. Status:', profile.status)
        router.push('/')
        return
      }

      setUser(user)
      console.log('✅ User access granted')
    } catch (error) {
      console.error('❌ Error checking user:', error)
      router.push('/auth/login')
    }
  }

  const loadStats = async () => {
    try {
      console.log('📊 Loading stats...')

      // Cargar estadísticas básicas primero
      let totalSongs = 0
      let totalPosts = 0
      let totalMessages = 0
      let totalEvents = 0
      let monthlyStreams = 0

      // Cargar canciones
      try {
        const { count } = await supabase.from('songs').select('id', { count: 'exact' })
        totalSongs = count || 0
        console.log('Songs count:', totalSongs)
      } catch (error) {
        console.log('Error loading songs:', error)
      }

      // Cargar posts
      try {
        const { count } = await supabase.from('blog_posts').select('id', { count: 'exact' })
        totalPosts = count || 0
        console.log('Posts count:', totalPosts)
      } catch (error) {
        console.log('Error loading posts:', error)
      }

      // Cargar mensajes
      try {
        const { count } = await supabase.from('contact_messages').select('id', { count: 'exact' })
        totalMessages = count || 0
        console.log('Messages count:', totalMessages)
      } catch (error) {
        console.log('Error loading messages:', error)
      }

      // Cargar eventos
      try {
        const { count } = await supabase.from('events').select('id', { count: 'exact' })
        totalEvents = count || 0
        console.log('Events count:', totalEvents)
      } catch (error) {
        console.log('Error loading events:', error)
      }

      // Cargar streams
      try {
        const { data: songsData } = await supabase
          .from('songs')
          .select('spotify_streams, youtube_views, apple_music_streams')
          .eq('status', 'published')

        monthlyStreams = songsData?.reduce((total, song) => {
          return total + (song.spotify_streams || 0) + (song.youtube_views || 0) + (song.apple_music_streams || 0)
        }, 0) || 0
        console.log('Total streams:', monthlyStreams)
      } catch (error) {
        console.log('Error loading streams:', error)
      }

      const newStats = {
        totalSongs,
        totalPosts,
        totalMessages,
        totalEvents,
        monthlyStreams
      }

      console.log('✅ Final stats:', newStats)
      setStats(newStats)
    } catch (error) {
      console.error('❌ Error loading stats:', error)
      // Set default stats in case of error
      setStats({
        totalSongs: 0,
        totalPosts: 0,
        totalMessages: 0,
        totalEvents: 0,
        monthlyStreams: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const quickActions = [
    {
      title: 'Nueva Canción',
      description: 'Agregar una nueva canción al catálogo',
      icon: '🎵',
      href: '/admin/songs/new',
      color: 'bg-blue-600'
    },
    {
      title: 'Nuevo Post',
      description: 'Crear un nuevo artículo para el blog',
      icon: '📝',
      href: '/admin/blog/nuevo',
      color: 'bg-green-600'
    },
    {
      title: 'Nuevo Evento',
      description: 'Programar un concierto o evento',
      icon: '🎪',
      href: '/admin/events/new',
      color: 'bg-purple-600'
    },
    {
      title: 'Ver Analytics',
      description: 'Revisar métricas y estadísticas',
      icon: '📊',
      href: '/admin/analytics',
      color: 'bg-orange-600'
    }
  ]

  const recentActivity = [
    { action: 'Nueva canción agregada', item: 'Reggaetón Chileno 2024', time: '2 horas' },
    { action: 'Post publicado', item: 'Historia del Reggaetón', time: '1 día' },
    { action: 'Mensaje recibido', item: 'Consulta de booking', time: '2 días' },
    { action: 'Press kit actualizado', item: 'Fotos promocionales', time: '3 días' }
  ]

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Panel de Administración</h1>
              <p className="text-gray-400">Bienvenido, {user.email}</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={() => router.push('/')}>
                Ver Sitio Web
              </Button>
              <Button variant="outline" onClick={handleSignOut}>
                Cerrar Sesión
              </Button>
            </div>
          </div>
        </Container>
      </header>

      {/* Dashboard Content */}
      <main className="section-padding">
        <Container>
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Streams</p>
                  <p className="text-3xl font-bold text-white">{stats.monthlyStreams.toLocaleString()}</p>
                  <p className="text-green-400 text-sm">+12.5% este mes</p>
                </div>
                <div className="text-3xl">📊</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Canciones</p>
                  <p className="text-3xl font-bold text-white">{stats.totalSongs}</p>
                  <p className="text-blue-400 text-sm">En catálogo</p>
                </div>
                <div className="text-3xl">🎵</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Eventos</p>
                  <p className="text-3xl font-bold text-white">{stats.totalEvents}</p>
                  <p className="text-purple-400 text-sm">Programados</p>
                </div>
                <div className="text-3xl">🎪</div>
              </div>
            </Card>

            <Card variant="glass" className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Posts del Blog</p>
                  <p className="text-3xl font-bold text-white">{stats.totalPosts}</p>
                  <p className="text-orange-400 text-sm">Publicados</p>
                </div>
                <div className="text-3xl">📝</div>
              </div>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Quick Actions */}
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">Acciones Rápidas</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <Card key={index} hover variant="glass" className="p-6 cursor-pointer" onClick={() => router.push(action.href)}>
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center text-2xl`}>
                        {action.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">{action.title}</h3>
                        <p className="text-gray-400 text-sm">{action.description}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">Actividad Reciente</h2>
              <Card variant="glass" className="p-6">
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
                      <div>
                        <p className="text-white font-medium">{activity.action}</p>
                        <p className="text-gray-400 text-sm">{activity.item}</p>
                      </div>
                      <div className="text-gray-400 text-sm">
                        hace {activity.time}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </div>

          {/* Management Sections */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-white mb-6">Gestión de Contenido</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Canciones', icon: '🎵', href: '/admin/songs', description: 'Gestionar catálogo musical' },
                { title: 'Blog', icon: '📝', href: '/admin/blog', description: 'Administrar posts y noticias' },
                { title: 'Eventos', icon: '🎪', href: '/admin/events', description: 'Gestionar conciertos y fechas' },
                { title: 'Press Kit', icon: '📁', href: '/admin/press-kit', description: 'Archivos promocionales' }
              ].map((section, index) => (
                <Card key={index} hover variant="glass" className="p-6 text-center cursor-pointer" onClick={() => router.push(section.href)}>
                  <div className="text-4xl mb-4">{section.icon}</div>
                  <h3 className="text-lg font-semibold text-white mb-2">{section.title}</h3>
                  <p className="text-gray-400 text-sm">{section.description}</p>
                </Card>
              ))}
            </div>
          </div>

          {/* Additional Modules */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-white mb-6">Administración</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Usuarios', icon: '👥', href: '/admin/usuarios', description: 'Gestionar registros y roles' },
                { title: 'Mensajes', icon: '📧', href: '/admin/messages', description: 'Ver mensajes de contacto' },
                { title: 'Analytics', icon: '📊', href: '/admin/analytics', description: 'Métricas y estadísticas' },
                { title: 'Configuración', icon: '⚙️', href: '/admin/settings', description: 'Configuración del sitio' }
              ].map((section, index) => (
                <Card key={index} hover variant="glass" className="p-6 text-center cursor-pointer" onClick={() => router.push(section.href)}>
                  <div className="text-4xl mb-4">{section.icon}</div>
                  <h3 className="text-lg font-semibold text-white mb-2">{section.title}</h3>
                  <p className="text-gray-400 text-sm">{section.description}</p>
                </Card>
              ))}
            </div>
          </div>
        </Container>
      </main>
    </div>
  )
}
