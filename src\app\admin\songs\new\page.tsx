'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { Song, User } from '@/types'

interface SongFormData {
  title: string
  artist: string
  album: string
  genre: string
  duration: number
  release_date: string
  featuring: string[]
  composers: string[]
  producers: string[]
  record_label: string
  isrc: string
  bpm: number
  key_signature: string
  explicit_content: boolean
  lyrics: string
  description: string
  status: 'draft' | 'published' | 'archived'
  featured: boolean
}

export default function NewSongPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [coverFile, setCoverFile] = useState<File | null>(null)
  const [formData, setFormData] = useState<SongFormData>({
    title: '',
    artist: 'Drago200',
    album: '',
    genre: 'reggaeton',
    duration: 0,
    release_date: '',
    featuring: [],
    composers: [],
    producers: [],
    record_label: '',
    isrc: '',
    bpm: 0,
    key_signature: '',
    explicit_content: false,
    lyrics: '',
    description: '',
    status: 'draft',
    featured: false
  })
  const router = useRouter()

  useEffect(() => {
    checkAccess()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'manager'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({ ...prev, [name]: checked }))
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleArrayInputChange = (field: keyof SongFormData, value: string) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item.length > 0)
    setFormData(prev => ({ ...prev, [field]: items }))
  }

  const uploadFile = async (file: File, bucket: string, folder: string) => {
    const fileExt = file.name.split('.').pop()
    const fileName = `${folder}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(fileName, file)

    if (error) throw error

    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(fileName)

    return publicUrl
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let audioUrl = ''
      let coverUrl = ''

      // Subir archivo de audio
      if (audioFile) {
        setUploading(true)
        audioUrl = await uploadFile(audioFile, 'music', 'songs')
      }

      // Subir cover
      if (coverFile) {
        coverUrl = await uploadFile(coverFile, 'music', 'covers')
      }

      // Crear la canción en la base de datos
      const songData = {
        ...formData,
        audio_url: audioUrl || null,
        cover_url: coverUrl || null,
        created_by: currentUser?.id,
        spotify_streams: 0,
        youtube_views: 0,
        apple_music_streams: 0
      }

      const { error } = await supabase
        .from('songs')
        .insert(songData)

      if (error) throw error

      router.push('/admin/songs')
    } catch (error) {
      console.error('Error creating song:', error)
      alert('Error al crear la canción')
    } finally {
      setLoading(false)
      setUploading(false)
    }
  }

  const genres = [
    'reggaeton', 'trap', 'hip hop', 'pop', 'r&b', 'latin', 'urban', 'dancehall'
  ]

  const keySignatures = [
    'C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'
  ]

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Nueva Canción</h1>
              <p className="text-gray-400">Agregar una nueva canción al catálogo</p>
            </div>
            <Button variant="ghost" onClick={() => router.push('/admin/songs')}>
              ← Volver a Canciones
            </Button>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Información Básica */}
              <Card variant="glass" className="p-6">
                <h2 className="text-xl font-bold text-white mb-6">Información Básica</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Título *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Artista
                    </label>
                    <input
                      type="text"
                      name="artist"
                      value={formData.artist}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Álbum
                      </label>
                      <input
                        type="text"
                        name="album"
                        value={formData.album}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Género
                      </label>
                      <select
                        name="genre"
                        value={formData.genre}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      >
                        {genres.map(genre => (
                          <option key={genre} value={genre}>
                            {genre.charAt(0).toUpperCase() + genre.slice(1)}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Duración (segundos)
                      </label>
                      <input
                        type="number"
                        name="duration"
                        value={formData.duration}
                        onChange={handleInputChange}
                        min="0"
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Fecha de Lanzamiento
                      </label>
                      <input
                        type="date"
                        name="release_date"
                        value={formData.release_date}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Featuring (separar con comas)
                    </label>
                    <input
                      type="text"
                      value={formData.featuring.join(', ')}
                      onChange={(e) => handleArrayInputChange('featuring', e.target.value)}
                      placeholder="Artista 1, Artista 2"
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Compositores (separar con comas)
                    </label>
                    <input
                      type="text"
                      value={formData.composers.join(', ')}
                      onChange={(e) => handleArrayInputChange('composers', e.target.value)}
                      placeholder="Compositor 1, Compositor 2"
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Productores (separar con comas)
                    </label>
                    <input
                      type="text"
                      value={formData.producers.join(', ')}
                      onChange={(e) => handleArrayInputChange('producers', e.target.value)}
                      placeholder="Productor 1, Productor 2"
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>
                </div>
              </Card>

              {/* Información Técnica */}
              <Card variant="glass" className="p-6">
                <h2 className="text-xl font-bold text-white mb-6">Información Técnica</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Sello Discográfico
                    </label>
                    <input
                      type="text"
                      name="record_label"
                      value={formData.record_label}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      ISRC
                    </label>
                    <input
                      type="text"
                      name="isrc"
                      value={formData.isrc}
                      onChange={handleInputChange}
                      placeholder="US-S1Z-99-00001"
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        BPM
                      </label>
                      <input
                        type="number"
                        name="bpm"
                        value={formData.bpm}
                        onChange={handleInputChange}
                        min="0"
                        max="300"
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Tonalidad
                      </label>
                      <select
                        name="key_signature"
                        value={formData.key_signature}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                      >
                        <option value="">Seleccionar</option>
                        {keySignatures.map(key => (
                          <option key={key} value={key}>{key}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Archivos */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Archivo de Audio (MP3)
                    </label>
                    <input
                      type="file"
                      accept="audio/mpeg,audio/mp3"
                      onChange={(e) => setAudioFile(e.target.files?.[0] || null)}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-drago-red file:text-white hover:file:bg-drago-red-dark"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Cover/Portada
                    </label>
                    <input
                      type="file"
                      accept="image/jpeg,image/png,image/webp"
                      onChange={(e) => setCoverFile(e.target.files?.[0] || null)}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-drago-red file:text-white hover:file:bg-drago-red-dark"
                    />
                  </div>

                  {/* Opciones */}
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="explicit_content"
                        checked={formData.explicit_content}
                        onChange={handleInputChange}
                        className="mr-2"
                      />
                      <label className="text-sm text-gray-300">
                        Contenido explícito
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="featured"
                        checked={formData.featured}
                        onChange={handleInputChange}
                        className="mr-2"
                      />
                      <label className="text-sm text-gray-300">
                        Canción destacada
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Estado
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    >
                      <option value="draft">Borrador</option>
                      <option value="published">Publicada</option>
                      <option value="archived">Archivada</option>
                    </select>
                  </div>
                </div>
              </Card>
            </div>

            {/* Descripción y Letra */}
            <Card variant="glass" className="p-6 mt-8">
              <h2 className="text-xl font-bold text-white mb-6">Contenido</h2>
              
              <div className="grid lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Descripción
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={6}
                    className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    placeholder="Descripción de la canción..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Letra
                  </label>
                  <textarea
                    name="lyrics"
                    value={formData.lyrics}
                    onChange={handleInputChange}
                    rows={6}
                    className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                    placeholder="Letra de la canción..."
                  />
                </div>
              </div>
            </Card>

            {/* Botones */}
            <div className="flex justify-end space-x-4 mt-8">
              <Button
                type="button"
                variant="ghost"
                onClick={() => router.push('/admin/songs')}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={loading || uploading}
                glow
              >
                {uploading ? 'Subiendo archivos...' : loading ? 'Guardando...' : 'Crear Canción'}
              </Button>
            </div>
          </form>
        </Container>
      </main>
    </div>
  )
}
