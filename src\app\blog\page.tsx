'use client'

import { useState } from 'react'
import Link from 'next/link'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export default function BlogPage() {
  const [activeCategory, setActiveCategory] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  const blogPosts = [
    {
      id: '1',
      title: 'El Reggaetón Chileno: 20 Años de Historia',
      slug: 'reggaeton-chileno-20-anos-historia',
      excerpt: 'Un recorrido por dos décadas de evolución del reggaetón en Chile, desde sus inicios hasta convertirse en un fenómeno cultural.',
      content: 'Contenido completo del artículo...',
      category: 'historia',
      author: 'Drago200',
      publishedAt: '2024-01-15',
      readTime: '8 min',
      featured: true,
      image: '/placeholder-blog-1.jpg',
      tags: ['reggaeton', 'historia', 'chile', 'musica urbana']
    },
    {
      id: '2',
      title: 'Nueva Colaboración Internacional en Camino',
      slug: 'nueva-colaboracion-internacional',
      excerpt: 'Drago200 anuncia una emocionante colaboración con artistas internacionales que promete revolucionar la escena urbana.',
      content: 'Contenido completo del artículo...',
      category: 'noticias',
      author: 'Equipo Drago200',
      publishedAt: '2024-01-10',
      readTime: '5 min',
      featured: true,
      image: '/placeholder-blog-2.jpg',
      tags: ['colaboracion', 'internacional', 'nuevo proyecto']
    },
    {
      id: '3',
      title: 'Behind the Scenes: Proceso Creativo del Nuevo Álbum',
      slug: 'behind-scenes-proceso-creativo',
      excerpt: 'Una mirada íntima al proceso de creación del próximo álbum de Drago200, desde la composición hasta la producción final.',
      content: 'Contenido completo del artículo...',
      category: 'musica',
      author: 'Drago200',
      publishedAt: '2024-01-05',
      readTime: '12 min',
      featured: false,
      image: '/placeholder-blog-3.jpg',
      tags: ['album', 'proceso creativo', 'estudio', 'produccion']
    },
    {
      id: '4',
      title: 'Gira Nacional 2024: Fechas y Ciudades Confirmadas',
      slug: 'gira-nacional-2024-fechas',
      excerpt: 'Se confirman las fechas y ciudades de la esperada gira nacional que llevará la música de Drago200 por todo Chile.',
      content: 'Contenido completo del artículo...',
      category: 'eventos',
      author: 'Management',
      publishedAt: '2023-12-28',
      readTime: '6 min',
      featured: true,
      image: '/placeholder-blog-4.jpg',
      tags: ['gira', 'conciertos', 'chile', '2024']
    },
    {
      id: '5',
      title: 'La Influencia del Reggaetón en la Cultura Juvenil',
      slug: 'influencia-reggaeton-cultura-juvenil',
      excerpt: 'Análisis del impacto cultural y social del reggaetón en las nuevas generaciones y su papel en la identidad musical chilena.',
      content: 'Contenido completo del artículo...',
      category: 'cultura',
      author: 'Drago200',
      publishedAt: '2023-12-20',
      readTime: '10 min',
      featured: false,
      image: '/placeholder-blog-5.jpg',
      tags: ['cultura', 'juventud', 'impacto social', 'identidad']
    },
    {
      id: '6',
      title: 'Consejos para Nuevos Artistas Urbanos',
      slug: 'consejos-nuevos-artistas-urbanos',
      excerpt: 'Drago200 comparte su experiencia y consejos para los artistas que están comenzando en la música urbana.',
      content: 'Contenido completo del artículo...',
      category: 'consejos',
      author: 'Drago200',
      publishedAt: '2023-12-15',
      readTime: '7 min',
      featured: false,
      image: '/placeholder-blog-6.jpg',
      tags: ['consejos', 'nuevos artistas', 'industria musical', 'experiencia']
    }
  ]

  const categories = [
    { id: 'all', label: 'Todas', count: blogPosts.length },
    { id: 'noticias', label: 'Noticias', count: blogPosts.filter(p => p.category === 'noticias').length },
    { id: 'musica', label: 'Música', count: blogPosts.filter(p => p.category === 'musica').length },
    { id: 'eventos', label: 'Eventos', count: blogPosts.filter(p => p.category === 'eventos').length },
    { id: 'historia', label: 'Historia', count: blogPosts.filter(p => p.category === 'historia').length },
    { id: 'cultura', label: 'Cultura', count: blogPosts.filter(p => p.category === 'cultura').length },
    { id: 'consejos', label: 'Consejos', count: blogPosts.filter(p => p.category === 'consejos').length }
  ]

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = activeCategory === 'all' || post.category === activeCategory
    const matchesSearch = searchTerm === '' || 
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    return matchesCategory && matchesSearch
  })

  const featuredPosts = blogPosts.filter(post => post.featured)
  const latestPosts = blogPosts.slice(0, 3)

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'noticias': return '📰'
      case 'musica': return '🎵'
      case 'eventos': return '🎤'
      case 'historia': return '📖'
      case 'cultura': return '🎭'
      case 'consejos': return '💡'
      default: return '📝'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-red-black">
        <Container>
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Blog & Noticias
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Mantente al día con las últimas noticias, reflexiones y actualizaciones 
              del mundo de Drago200 y la música urbana chilena.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-md mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Buscar artículos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 rounded-lg bg-white border border-gray-300 text-black placeholder-gray-500 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none"
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Featured Posts */}
      {featuredPosts.length > 0 && (
        <section className="py-16 bg-drago-black-light">
          <Container>
            <h2 className="text-3xl font-bold text-white mb-8">Artículos Destacados</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredPosts.map((post) => (
                <Card key={post.id} hover variant="glass" className="overflow-hidden">
                  <div className="h-48 bg-gradient-red-black flex items-center justify-center">
                    <span className="text-4xl">📰</span>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-lg">{getCategoryIcon(post.category)}</span>
                      <span className="text-drago-red text-sm font-semibold uppercase">
                        {post.category}
                      </span>
                      <span className="text-gray-400 text-sm">•</span>
                      <span className="text-gray-400 text-sm">{post.readTime}</span>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="text-gray-400 text-xs">
                        {formatDate(post.publishedAt)}
                      </div>
                      <Link href={`/blog/${post.slug}`}>
                        <Button variant="ghost" size="sm">
                          Leer más →
                        </Button>
                      </Link>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </Container>
        </section>
      )}

      {/* Categories Filter */}
      <section className="py-8 bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={activeCategory === category.id ? 'primary' : 'ghost'}
                onClick={() => setActiveCategory(category.id)}
                size="sm"
                className="min-w-[100px]"
              >
                {category.label} ({category.count})
              </Button>
            ))}
          </div>
        </Container>
      </section>

      {/* All Posts */}
      <section className="section-padding">
        <Container>
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <h2 className="text-3xl font-bold text-white mb-8">
                {activeCategory === 'all' ? 'Todos los Artículos' : `Categoría: ${categories.find(c => c.id === activeCategory)?.label}`}
              </h2>
              
              {filteredPosts.length === 0 ? (
                <Card variant="glass" className="p-8 text-center">
                  <p className="text-gray-400">No se encontraron artículos que coincidan con tu búsqueda.</p>
                </Card>
              ) : (
                <div className="space-y-8">
                  {filteredPosts.map((post) => (
                    <Card key={post.id} hover variant="glass" className="p-6">
                      <div className="flex flex-col md:flex-row gap-6">
                        <div className="md:w-48 h-32 bg-gradient-red-black rounded-lg flex items-center justify-center flex-shrink-0">
                          <span className="text-3xl">{getCategoryIcon(post.category)}</span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-3">
                            <span className="text-drago-red text-sm font-semibold uppercase">
                              {post.category}
                            </span>
                            <span className="text-gray-400 text-sm">•</span>
                            <span className="text-gray-400 text-sm">{post.readTime}</span>
                            <span className="text-gray-400 text-sm">•</span>
                            <span className="text-gray-400 text-sm">{formatDate(post.publishedAt)}</span>
                          </div>
                          <h3 className="text-2xl font-bold text-white mb-3">
                            {post.title}
                          </h3>
                          <p className="text-gray-300 mb-4 line-clamp-2">
                            {post.excerpt}
                          </p>
                          <div className="flex items-center justify-between">
                            <div className="text-gray-400 text-sm">
                              Por {post.author}
                            </div>
                            <Link href={`/blog/${post.slug}`}>
                              <Button variant="ghost" size="sm">
                                Leer artículo completo →
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Latest Posts */}
              <Card variant="glass" className="p-6">
                <h3 className="text-xl font-bold text-white mb-6">Últimos Artículos</h3>
                <div className="space-y-4">
                  {latestPosts.map((post) => (
                    <div key={post.id} className="border-b border-gray-700 pb-4 last:border-b-0">
                      <Link href={`/blog/${post.slug}`}>
                        <h4 className="text-white font-semibold mb-2 hover:text-drago-red transition-colors line-clamp-2">
                          {post.title}
                        </h4>
                      </Link>
                      <p className="text-gray-400 text-sm">{formatDate(post.publishedAt)}</p>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Newsletter Signup */}
              <Card variant="gradient" className="p-6 text-center">
                <h3 className="text-xl font-bold text-white mb-4">
                  Suscríbete al Newsletter
                </h3>
                <p className="text-gray-300 text-sm mb-6">
                  Recibe las últimas noticias y actualizaciones directamente en tu email.
                </p>
                <div className="space-y-4">
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-2 rounded-lg bg-white border border-gray-300 text-black placeholder-gray-500 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none"
                  />
                  <Button size="sm" className="w-full">
                    Suscribirse
                  </Button>
                </div>
              </Card>

              {/* Social Media */}
              <Card variant="glass" className="p-6">
                <h3 className="text-xl font-bold text-white mb-6">Síguenos</h3>
                <div className="space-y-3">
                  {[
                    { name: 'Instagram', icon: '📷', followers: '500K' },
                    { name: 'YouTube', icon: '📺', followers: '300K' },
                    { name: 'TikTok', icon: '🎬', followers: '800K' },
                    { name: 'Twitter', icon: '🐦', followers: '200K' }
                  ].map((social) => (
                    <div key={social.name} className="flex items-center justify-between p-3 bg-drago-black-light rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="text-xl">{social.icon}</span>
                        <span className="text-white font-medium">{social.name}</span>
                      </div>
                      <span className="text-gray-400 text-sm">{social.followers}</span>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </div>
        </Container>
      </section>
    </div>
  )
}
