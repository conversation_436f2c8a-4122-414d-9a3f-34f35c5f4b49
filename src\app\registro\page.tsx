'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { UserRegistrationForm } from '@/types'

export default function RegistroPage() {
  const [formData, setFormData] = useState<UserRegistrationForm>({
    nombres: '',
    apellidos: '',
    fecha_nacimiento: '',
    pais: '',
    edad: 0,
    email: '',
    mensaje: ''
  })
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const paisesLatam = [
    'Argentina', 'Bolivia', 'Brasil', 'Chile', 'Colombia', 'Costa Rica',
    'Cuba', 'Ecuador', 'El Salvador', 'Guatemala', 'Honduras', 'México',
    'Nicaragua', 'Panamá', 'Paraguay', 'Perú', 'República Dominicana',
    'Uruguay', 'Venezuela'
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ 
      ...prev, 
      [name]: name === 'edad' ? parseInt(value) || 0 : value 
    }))
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const birthDate = e.target.value
    const age = calculateAge(birthDate)
    
    setFormData(prev => ({
      ...prev,
      fecha_nacimiento: birthDate,
      edad: age
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // Validaciones
      if (formData.edad < 18) {
        setError('Debes ser mayor de 18 años para registrarte')
        setLoading(false)
        return
      }

      // Verificar si el email ya existe
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('email')
        .eq('email', formData.email)
        .single()

      if (existingUser) {
        setError('Este email ya está registrado')
        setLoading(false)
        return
      }

      // Crear registro pendiente
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          email: formData.email,
          nombres: formData.nombres,
          apellidos: formData.apellidos,
          fecha_nacimiento: formData.fecha_nacimiento,
          pais: formData.pais,
          edad: formData.edad,
          role: 'pending',
          status: 'pending'
        })

      if (insertError) {
        throw insertError
      }

      setSubmitted(true)
    } catch (error: any) {
      console.error('Error al registrar usuario:', error)
      setError(error.message || 'Error al procesar el registro')
    } finally {
      setLoading(false)
    }
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <Container size="sm">
          <Card variant="glass" className="p-8 text-center">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">¡Registro Enviado!</h2>
            <p className="text-gray-300 mb-6">
              Tu solicitud de registro ha sido enviada exitosamente. Un administrador revisará tu solicitud y te contactará pronto.
            </p>
            <div className="space-y-4">
              <Button onClick={() => router.push('/')} variant="primary">
                Volver al Inicio
              </Button>
              <Button onClick={() => router.push('/auth/login')} variant="outline">
                Ir al Login
              </Button>
            </div>
          </Card>
        </Container>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black py-24">
      <Container size="md">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Únete al Equipo de <span className="text-drago-red">Drago200</span>
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            ¿Quieres formar parte del equipo de trabajo de Drago200? Completa este formulario 
            y un administrador revisará tu solicitud.
          </p>
        </div>

        <Card variant="glass" className="p-8 max-w-2xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-500/20 border border-red-500 text-red-300 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white font-semibold mb-2">
                  Nombres *
                </label>
                <input
                  type="text"
                  name="nombres"
                  value={formData.nombres}
                  onChange={handleInputChange}
                  placeholder="Tus nombres"
                  className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                  required
                />
              </div>

              <div>
                <label className="block text-white font-semibold mb-2">
                  Apellidos *
                </label>
                <input
                  type="text"
                  name="apellidos"
                  value={formData.apellidos}
                  onChange={handleInputChange}
                  placeholder="Tus apellidos"
                  className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-white font-semibold mb-2">
                Email *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white font-semibold mb-2">
                  Fecha de Nacimiento *
                </label>
                <input
                  type="date"
                  name="fecha_nacimiento"
                  value={formData.fecha_nacimiento}
                  onChange={handleDateChange}
                  className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                  required
                />
              </div>

              <div>
                <label className="block text-white font-semibold mb-2">
                  Edad
                </label>
                <input
                  type="number"
                  name="edad"
                  value={formData.edad || ''}
                  readOnly
                  className="w-full bg-gray-200 border border-gray-300 text-gray-600 rounded-lg px-4 py-2 cursor-not-allowed"
                  placeholder="Se calcula automáticamente"
                />
              </div>
            </div>

            <div>
              <label className="block text-white font-semibold mb-2">
                País (LATAM) *
              </label>
              <select
                name="pais"
                value={formData.pais}
                onChange={handleInputChange}
                className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                required
              >
                <option value="">Selecciona tu país</option>
                {paisesLatam.map(pais => (
                  <option key={pais} value={pais}>{pais}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-white font-semibold mb-2">
                Mensaje (Opcional)
              </label>
              <textarea
                name="mensaje"
                value={formData.mensaje}
                onChange={handleInputChange}
                rows={4}
                placeholder="Cuéntanos por qué quieres unirte al equipo de Drago200..."
                className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors resize-vertical"
              />
            </div>

            <div className="bg-drago-black-light p-4 rounded-lg">
              <h3 className="text-white font-semibold mb-2">Roles Disponibles:</h3>
              <ul className="text-gray-300 text-sm space-y-1">
                <li>• <strong>Periodista:</strong> Crear noticias y artículos</li>
                <li>• <strong>Booking:</strong> Gestionar eventos y páginas de invitación</li>
                <li>• <strong>Manager:</strong> Administrar catálogo de música</li>
                <li>• <strong>Moderador:</strong> Supervisar contenido general</li>
              </ul>
              <p className="text-gray-400 text-xs mt-2">
                El rol será asignado por un administrador según tu perfil y experiencia.
              </p>
            </div>

            <Button
              type="submit"
              size="lg"
              glow
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Enviando Registro...' : 'Enviar Solicitud'}
            </Button>
          </form>
        </Card>

        <div className="text-center mt-8">
          <p className="text-gray-400 text-sm">
            ¿Ya tienes una cuenta aprobada?{' '}
            <button
              onClick={() => router.push('/auth/login')}
              className="text-drago-red hover:underline"
            >
              Inicia sesión aquí
            </button>
          </p>
        </div>
      </Container>
    </div>
  )
}
