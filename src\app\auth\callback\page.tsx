'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'

export default function AuthCallback() {
  const router = useRouter()

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('Procesando callback...')

        // Esperar un poco para que Supabase procese la autenticación
        await new Promise(resolve => setTimeout(resolve, 2000))

        const { data: { user }, error } = await supabase.auth.getUser()

        console.log('Usuario:', user)
        console.log('Error:', error)

        if (error || !user) {
          console.log('Sin usuario, redirigiendo a login')
          window.location.href = '/auth/login'
          return
        }

        console.log('Usuario autenticado:', user.email)

        // Buscar perfil
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('auth_user_id', user.id)
          .single()

        console.log('Perfil:', profile)

        if (!profile) {
          console.log('Sin perfil, redirigiendo a completar')
          window.location.href = '/auth/complete-profile'
          return
        }

        if (profile.status === 'approved') {
          console.log('Usuario aprobado, redirigiendo a admin')
          window.location.href = '/admin'
        } else {
          console.log('Usuario pendiente, redirigiendo a home')
          window.location.href = '/?status=pending'
        }

      } catch (err) {
        console.error('Error en callback:', err)
        window.location.href = '/auth/login'
      }
    }

    // Timeout de seguridad
    const timeout = setTimeout(() => {
      console.log('Timeout alcanzado, redirigiendo a home')
      window.location.href = '/'
    }, 10000)

    handleCallback().finally(() => {
      clearTimeout(timeout)
    })
  }, [])

  return (
    <div className="min-h-screen bg-drago-black flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 bg-drago-red rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
          <span className="text-white font-bold text-xl">D</span>
        </div>
        <h2 className="text-xl font-semibold text-white mb-2">Procesando autenticación...</h2>
        <p className="text-gray-400 mb-4">Por favor espera un momento</p>

        {/* Botón de emergencia después de 5 segundos */}
        <button
          onClick={() => router.replace('/')}
          className="mt-4 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
        >
          Si esto toma mucho tiempo, haz clic aquí
        </button>
      </div>
    </div>
  )
}
