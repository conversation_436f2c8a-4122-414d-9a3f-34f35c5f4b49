'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { Song, User } from '@/types'

export default function SongsAdminPage() {
  const [songs, setSongs] = useState<Song[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'published' | 'draft' | 'archived'>('all')
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const router = useRouter()

  useEffect(() => {
    checkAccess()
    loadSongs()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'manager'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const loadSongs = async () => {
    try {
      const { data, error } = await supabase
        .from('songs')
        .select(`
          *,
          creator:profiles!songs_created_by_fkey(nombres, apellidos, role)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setSongs(data || [])
    } catch (error) {
      console.error('Error loading songs:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSong = async (songId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta canción?')) return

    try {
      const { error } = await supabase
        .from('songs')
        .delete()
        .eq('id', songId)

      if (error) throw error
      loadSongs()
    } catch (error) {
      console.error('Error deleting song:', error)
    }
  }

  const handleToggleStatus = async (songId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'published' ? 'draft' : 'published'
    
    try {
      const { error } = await supabase
        .from('songs')
        .update({ status: newStatus })
        .eq('id', songId)

      if (error) throw error
      loadSongs()
    } catch (error) {
      console.error('Error updating song status:', error)
    }
  }

  const handleToggleFeatured = async (songId: string, currentFeatured: boolean) => {
    try {
      const { error } = await supabase
        .from('songs')
        .update({ featured: !currentFeatured })
        .eq('id', songId)

      if (error) throw error
      loadSongs()
    } catch (error) {
      console.error('Error updating featured status:', error)
    }
  }

  const filteredSongs = songs.filter(song => {
    if (filter === 'all') return true
    return song.status === filter
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'text-green-400'
      case 'draft': return 'text-yellow-400'
      case 'archived': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  const getGenreColor = (genre: string) => {
    switch (genre?.toLowerCase()) {
      case 'reggaeton': return 'bg-red-600'
      case 'trap': return 'bg-purple-600'
      case 'hip hop': return 'bg-blue-600'
      case 'pop': return 'bg-pink-600'
      default: return 'bg-gray-600'
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatStreams = (streams?: number) => {
    if (!streams) return '0'
    if (streams >= 1000000) return `${(streams / 1000000).toFixed(1)}M`
    if (streams >= 1000) return `${(streams / 1000).toFixed(1)}K`
    return streams.toString()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando canciones...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Gestión de Canciones</h1>
              <p className="text-gray-400">Administra el catálogo musical</p>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/songs/new">
                <Button variant="primary" glow>
                  + Nueva Canción
                </Button>
              </Link>
              <Button variant="ghost" onClick={() => router.push('/admin')}>
                ← Dashboard
              </Button>
            </div>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          {/* Filtros */}
          <div className="flex flex-wrap gap-4 mb-8">
            {[
              { key: 'all', label: 'Todas', count: songs.length },
              { key: 'published', label: 'Publicadas', count: songs.filter(s => s.status === 'published').length },
              { key: 'draft', label: 'Borradores', count: songs.filter(s => s.status === 'draft').length },
              { key: 'archived', label: 'Archivadas', count: songs.filter(s => s.status === 'archived').length }
            ].map(filterOption => (
              <Button
                key={filterOption.key}
                variant={filter === filterOption.key ? 'primary' : 'ghost'}
                onClick={() => setFilter(filterOption.key as any)}
                size="sm"
              >
                {filterOption.label} ({filterOption.count})
              </Button>
            ))}
          </div>

          {/* Lista de Canciones */}
          <div className="space-y-4">
            {filteredSongs.map(song => (
              <Card key={song.id} variant="glass" className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex items-start gap-4 flex-1">
                    {/* Cover */}
                    <div className="w-16 h-16 bg-drago-black-light rounded-lg flex items-center justify-center overflow-hidden">
                      {song.cover_url ? (
                        <img src={song.cover_url} alt={song.title} className="w-full h-full object-cover" />
                      ) : (
                        <span className="text-2xl">🎵</span>
                      )}
                    </div>

                    {/* Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-white">
                          {song.title}
                        </h3>
                        {song.genre && (
                          <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getGenreColor(song.genre)}`}>
                            {song.genre}
                          </span>
                        )}
                        {song.featured && (
                          <span className="px-2 py-1 rounded text-xs font-semibold text-white bg-drago-red">
                            Destacada
                          </span>
                        )}
                        <span className={`text-sm font-medium ${getStatusColor(song.status)}`}>
                          {song.status}
                        </span>
                      </div>
                      
                      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-300 mb-3">
                        <div>
                          <span className="text-gray-400">Artista:</span> {song.artist}
                        </div>
                        <div>
                          <span className="text-gray-400">Duración:</span> {formatDuration(song.duration)}
                        </div>
                        <div>
                          <span className="text-gray-400">Streams:</span> {formatStreams(song.spotify_streams)}
                        </div>
                        <div>
                          <span className="text-gray-400">Lanzamiento:</span> {song.release_date ? new Date(song.release_date).toLocaleDateString('es-CL') : 'N/A'}
                        </div>
                      </div>

                      {/* Colaboradores */}
                      {(song.featuring && song.featuring.length > 0) && (
                        <div className="mb-2">
                          <span className="text-gray-400 text-sm">Featuring: </span>
                          <span className="text-gray-300 text-sm">{song.featuring.join(', ')}</span>
                        </div>
                      )}

                      {/* Productores */}
                      {(song.producers && song.producers.length > 0) && (
                        <div className="mb-2">
                          <span className="text-gray-400 text-sm">Productores: </span>
                          <span className="text-gray-300 text-sm">{song.producers.join(', ')}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Acciones */}
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Link href={`/admin/songs/edit/${song.id}`}>
                      <Button variant="ghost" size="sm">
                        ✏️ Editar
                      </Button>
                    </Link>
                    
                    <Button
                      variant={song.status === 'published' ? "outline" : "primary"}
                      size="sm"
                      onClick={() => handleToggleStatus(song.id, song.status)}
                    >
                      {song.status === 'published' ? '📝 Despublicar' : '🚀 Publicar'}
                    </Button>

                    <Button
                      variant={song.featured ? "outline" : "ghost"}
                      size="sm"
                      onClick={() => handleToggleFeatured(song.id, song.featured)}
                    >
                      {song.featured ? '⭐ Quitar destacada' : '⭐ Destacar'}
                    </Button>

                    {song.audio_url && (
                      <Button variant="ghost" size="sm">
                        🎧 Reproducir
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteSong(song.id)}
                      className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                    >
                      🗑️ Eliminar
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {filteredSongs.length === 0 && (
            <Card variant="glass" className="p-8 text-center">
              <p className="text-gray-400 mb-4">No hay canciones que coincidan con el filtro seleccionado.</p>
              <Link href="/admin/songs/new">
                <Button variant="primary">
                  Agregar la primera canción
                </Button>
              </Link>
            </Card>
          )}
        </Container>
      </main>
    </div>
  )
}
