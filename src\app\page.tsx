'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import Container from '@/components/layout/Container'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false)
  const [statusMessage, setStatusMessage] = useState('')

  useEffect(() => {
    setIsLoaded(true)

    // Obtener parámetros de URL de forma más simple
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const status = urlParams.get('status')

      if (status === 'pending') {
        setStatusMessage('Tu solicitud está pendiente de aprobación por un administrador.')
      } else if (status === 'denied') {
        setStatusMessage('Tu solicitud ha sido denegada. Contacta al administrador para más información.')
      } else if (status === 'unauthorized') {
        setStatusMessage('No tienes permisos para acceder al panel de administración.')
      }
    }
  }, [])

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Status Message */}
      {statusMessage && (
        <div className="bg-yellow-600 text-white px-4 py-3 text-center">
          <p>{statusMessage}</p>
        </div>
      )}
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-red-black opacity-90"></div>

        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-drago-red/20 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-drago-red/10 rounded-full blur-3xl animate-pulse-slow delay-1000"></div>
        </div>

        <Container className="relative z-10 text-center">
          <div className={`transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            {/* Logo/Artist Name */}
            <h1 className="font-display text-6xl md:text-8xl lg:text-9xl font-bold text-white mb-6 tracking-wider">
              DRAGO<span className="text-drago-red">200</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Co-fundador de Shishigang • Máquina para hacer hits • Artista de Rimas Entertainment
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button size="lg" glow className="min-w-[200px]">
                Escuchar Música
              </Button>
              <Button variant="outline" size="lg" className="min-w-[200px]">
                Ver Biografía
              </Button>
            </div>

            {/* Latest Release Preview */}
            <div className="mt-16">
              <p className="text-drago-red font-semibold mb-4">ÚLTIMO LANZAMIENTO</p>
              <Card variant="glass" className="max-w-md mx-auto p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-red-gold rounded-lg flex items-center justify-center">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1 text-left">
                    <h3 className="font-semibold text-white">Mojaita</h3>
                    <p className="text-gray-400 text-sm">Drago200 ft. Kidd Voodoo, Kaydy Cain</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    ▶
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </Container>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Featured Sections Preview */}
      <section className="section-padding">
        <Container>
          <div className="grid md:grid-cols-3 gap-8">
            {/* Music Section */}
            <Card hover variant="glass" className="p-8 text-center">
              <div className="w-16 h-16 bg-drago-red rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12a7.971 7.971 0 00-1.343-4.243 1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Música</h3>
              <p className="text-gray-400 mb-4">Descubre los éxitos que marcaron una generación</p>
              <Link href="/musica">
                <Button variant="ghost" size="sm">Explorar →</Button>
              </Link>
            </Card>

            {/* Biography Section */}
            <Card hover variant="glass" className="p-8 text-center">
              <div className="w-16 h-16 bg-drago-red rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Biografía</h3>
              <p className="text-gray-400 mb-4">La historia del pionero del reggaetón chileno</p>
              <Link href="/biografia">
                <Button variant="ghost" size="sm">Leer más →</Button>
              </Link>
            </Card>

            {/* Press Kit Section */}
            <Card hover variant="glass" className="p-8 text-center">
              <div className="w-16 h-16 bg-drago-red rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Press Kit</h3>
              <p className="text-gray-400 mb-4">Recursos para medios y profesionales</p>
              <Link href="/press-kit">
                <Button variant="ghost" size="sm">Descargar →</Button>
              </Link>
            </Card>
          </div>
        </Container>
      </section>

      {/* Latest News Section */}
      <section className="section-padding bg-drago-black-light">
        <Container>
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">Últimas Noticias</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Mantente al día con las últimas novedades, lanzamientos y eventos
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((item) => (
              <Card key={item} hover variant="default" className="overflow-hidden">
                <div className="h-48 bg-gradient-red-black"></div>
                <div className="p-6">
                  <div className="text-drago-red text-sm font-semibold mb-2">
                    {new Date().toLocaleDateString('es-CL')}
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Título de la noticia {item}
                  </h3>
                  <p className="text-gray-400 mb-4">
                    Breve descripción de la noticia que captura la atención del lector...
                  </p>
                  <Button variant="ghost" size="sm">
                    Leer más →
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/blog">
              <Button variant="outline" size="lg">
                Ver todas las noticias
              </Button>
            </Link>
          </div>
        </Container>
      </section>
    </div>
  )
}
