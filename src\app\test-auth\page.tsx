'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestAuthPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testConnection = async () => {
    setLoading(true)
    try {
      console.log('🧪 Testing Supabase connection...')
      
      // Test 1: Verificar conexión básica
      const { data, error } = await supabase.from('profiles').select('count').limit(1)
      console.log('Connection test:', { data, error })
      
      // Test 2: Verificar sesión actual
      const { data: session } = await supabase.auth.getSession()
      console.log('Current session:', session)
      
      // Test 3: Verificar usuario actual
      const { data: user } = await supabase.auth.getUser()
      console.log('Current user:', user)
      
      setResult({
        connection: { data, error },
        session: session,
        user: user,
        timestamp: new Date().toISOString()
      })
      
    } catch (error) {
      console.error('Test failed:', error)
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGoogleAuth = async () => {
    try {
      console.log('🔑 Testing Google Auth...')
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/api/auth/callback`
        }
      })
      
      console.log('Google auth result:', { data, error })
      
      if (error) {
        setResult({ googleAuthError: error })
      }
    } catch (error) {
      console.error('Google auth failed:', error)
      setResult({ googleAuthError: error.message })
    }
  }

  const testEmailAuth = async () => {
    try {
      console.log('📧 Testing Email Auth...')
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'test123' // Esto fallará, pero nos dirá si la conexión funciona
      })
      
      console.log('Email auth result:', { data, error })
      setResult({ emailAuthTest: { data, error } })
    } catch (error) {
      console.error('Email auth failed:', error)
      setResult({ emailAuthError: error.message })
    }
  }

  return (
    <div className="min-h-screen bg-drago-black p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">🧪 Test de Autenticación</h1>
        
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">Tests Disponibles</h2>
            <div className="space-y-4">
              <button
                onClick={testConnection}
                disabled={loading}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? '⏳ Probando...' : '🔌 Test Conexión'}
              </button>
              
              <button
                onClick={testGoogleAuth}
                className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                🔑 Test Google Auth
              </button>
              
              <button
                onClick={testEmailAuth}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                📧 Test Email Auth
              </button>
            </div>
          </div>
          
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">Configuración</h2>
            <div className="space-y-2 text-sm text-gray-300">
              <div>
                <strong>Supabase URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL}
              </div>
              <div>
                <strong>Anon Key:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...
              </div>
              <div>
                <strong>Site URL:</strong> {window.location.origin}
              </div>
              <div>
                <strong>Callback URL:</strong> {window.location.origin}/api/auth/callback
              </div>
            </div>
          </div>
        </div>
        
        {result && (
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">📊 Resultados</h2>
            <pre className="text-sm text-gray-300 overflow-auto bg-gray-900 p-4 rounded">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
        
        <div className="mt-8 flex space-x-4">
          <a
            href="/debug"
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            🔍 Ir a Debug
          </a>
          <a
            href="/auth/login"
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            📝 Ir a Login
          </a>
          <a
            href="/admin"
            className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
          >
            ⚙️ Ir a Admin
          </a>
        </div>
      </div>
    </div>
  )
}
