import React from 'react'
import { cn } from '@/utils/cn'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'gradient'
  hover?: boolean
  glow?: boolean
  children: React.ReactNode
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', hover = false, glow = false, children, ...props }, ref) => {
    const baseClasses = 'rounded-lg transition-all duration-300'
    
    const variants = {
      default: 'bg-drago-black-light border border-gray-700',
      glass: 'bg-drago-black-light/80 backdrop-blur-md border border-gray-600/50',
      gradient: 'bg-gradient-red-black border border-drago-red/30'
    }
    
    const hoverClass = hover ? 'hover:transform hover:scale-105 hover:shadow-glow-red cursor-pointer' : ''
    const glowClass = glow ? 'shadow-glow-red' : ''
    
    return (
      <div
        className={cn(
          baseClasses,
          variants[variant],
          hoverClass,
          glowClass,
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    )
  }
)

Card.displayName = 'Card'

export default Card
