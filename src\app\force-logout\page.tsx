'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

export default function ForceLogoutPage() {
  const [status, setStatus] = useState('Iniciando logout forzado...')
  const router = useRouter()

  useEffect(() => {
    forceLogout()
  }, [])

  const forceLogout = async () => {
    try {
      setStatus('🔄 Cerrando sesión...')
      
      // 1. Cerrar sesión en Supabase
      await supabase.auth.signOut()
      setStatus('✅ Sesión cerrada en Supabase')
      
      // 2. Limpiar localStorage
      localStorage.clear()
      setStatus('✅ LocalStorage limpiado')
      
      // 3. Limpiar sessionStorage
      sessionStorage.clear()
      setStatus('✅ SessionStorage limpiado')
      
      // 4. Esperar un momento
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setStatus('✅ Logout completado. Redirigiendo...')
      
      // 5. Redirigir al home
      setTimeout(() => {
        window.location.href = '/'
      }, 1000)
      
    } catch (error) {
      console.error('Error en logout:', error)
      setStatus('❌ Error en logout: ' + error.message)
    }
  }

  const manualRedirect = () => {
    window.location.href = '/'
  }

  return (
    <div className="min-h-screen bg-drago-black flex items-center justify-center">
      <div className="text-center p-8">
        <h1 className="text-2xl font-bold text-white mb-4">🚪 Logout Forzado</h1>
        <p className="text-gray-300 mb-6">{status}</p>
        
        <div className="space-y-4">
          <button
            onClick={forceLogout}
            className="px-6 py-3 bg-red-600 text-white rounded hover:bg-red-700"
          >
            🔄 Repetir Logout
          </button>
          
          <button
            onClick={manualRedirect}
            className="px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 ml-4"
          >
            🏠 Ir al Home
          </button>
        </div>
        
        <div className="mt-8 text-sm text-gray-400">
          <p>Si sigues teniendo problemas:</p>
          <p>1. Cierra todas las pestañas del navegador</p>
          <p>2. Abre una nueva ventana de incógnito</p>
          <p>3. Ve a localhost:3000</p>
        </div>
      </div>
    </div>
  )
}
