@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Oswald:wght@200;300;400;500;600;700&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS personalizadas para Drago200 */
:root {
  /* Colores principales */
  --drago-red: #dc2626;
  --drago-red-dark: #991b1b;
  --drago-red-light: #f87171;
  --drago-black: #0f172a;
  --drago-black-light: #1e293b;
  --drago-black-dark: #020617;
  --drago-gold: #fbbf24;
  --drago-gold-dark: #d97706;

  /* Gradientes */
  --gradient-red-black: linear-gradient(135deg, var(--drago-red) 0%, var(--drago-black) 100%);
  --gradient-black-red: linear-gradient(135deg, var(--drago-black) 0%, var(--drago-red) 100%);
  --gradient-red-gold: linear-gradient(135deg, var(--drago-red) 0%, var(--drago-gold) 100%);

  /* Sombras */
  --shadow-glow-red: 0 0 20px rgba(220, 38, 38, 0.5);
  --shadow-glow-red-lg: 0 0 40px rgba(220, 38, 38, 0.5);

  /* Tipografía */
  --font-display: 'Oswald', system-ui, sans-serif;
  --font-body: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}

/* Reset y estilos base */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--drago-black);
  color: #ffffff;
  font-family: var(--font-body);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Estilos para elementos de texto */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 600;
  line-height: 1.2;
  color: #ffffff;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
}

/* Enlaces */
a {
  color: var(--drago-red);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--drago-red-light);
  text-shadow: var(--shadow-glow-red);
}

/* Botones base */
.btn {
  @apply px-6 py-3 rounded-lg font-semibold transition-all duration-300 cursor-pointer inline-flex items-center justify-center;
}

.btn-primary {
  @apply bg-drago-red text-white hover:bg-drago-red-dark hover:shadow-glow-red;
}

.btn-secondary {
  @apply bg-transparent border-2 border-drago-red text-drago-red hover:bg-drago-red hover:text-white;
}

.btn-ghost {
  @apply bg-transparent text-white hover:bg-drago-red hover:text-white;
}

/* Efectos de glow */
.glow-red {
  box-shadow: var(--shadow-glow-red);
}

.glow-red-lg {
  box-shadow: var(--shadow-glow-red-lg);
}

/* Gradientes de fondo */
.bg-gradient-red-black {
  background: var(--gradient-red-black);
}

.bg-gradient-black-red {
  background: var(--gradient-black-red);
}

.bg-gradient-red-gold {
  background: var(--gradient-red-gold);
}

/* Animaciones personalizadas */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--drago-red), 0 0 10px var(--drago-red);
  }
  50% {
    box-shadow: 0 0 10px var(--drago-red), 0 0 20px var(--drago-red), 0 0 30px var(--drago-red);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--drago-black-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--drago-red);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--drago-red-light);
}

/* Selección de texto */
::selection {
  background: var(--drago-red);
  color: white;
}

/* Estilos para formularios */
input, textarea, select {
  @apply bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors;
}

input::placeholder, textarea::placeholder {
  @apply text-gray-500;
}

/* Inputs específicos con fondo oscuro */
input.dark-input, textarea.dark-input, select.dark-input {
  @apply bg-drago-black-light border border-gray-600 text-white;
}

input.dark-input::placeholder, textarea.dark-input::placeholder {
  @apply text-gray-400;
}

/* Inputs deshabilitados */
input:disabled, textarea:disabled, select:disabled {
  @apply bg-gray-200 text-gray-500 cursor-not-allowed;
}

input.dark-input:disabled, textarea.dark-input:disabled, select.dark-input:disabled {
  @apply bg-gray-700 text-gray-400 cursor-not-allowed;
}

/* Estilos para el reproductor de música */
.music-player {
  @apply bg-drago-black-light border border-gray-700 rounded-lg p-4;
}

/* Efectos de hover para cards */
.card-hover {
  @apply transition-all duration-300 hover:transform hover:scale-105 hover:shadow-glow-red;
}

/* Estilos para el header/navegación */
.nav-link {
  @apply text-white hover:text-drago-red transition-colors duration-300 relative;
}

.nav-link::after {
  content: '';
  @apply absolute bottom-0 left-0 w-0 h-0.5 bg-drago-red transition-all duration-300;
}

.nav-link:hover::after {
  @apply w-full;
}

/* Estilos para secciones */
.section-padding {
  @apply py-16 md:py-24;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .section-padding {
    @apply py-12;
  }
}
