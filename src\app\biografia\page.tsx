'use client'

import { useState } from 'react'
import Image from 'next/image'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export default function BiografiaPage() {
  const [activeTab, setActiveTab] = useState('historia')

  const timelineEvents = [
    {
      year: '2016',
      title: 'Los Inicios',
      description: '<PERSON> inicia su carrera musical como Drago200 y co-funda Shishigang, un influyente colectivo chileno de música urbana.',
      image: '/placeholder-timeline-1.jpg'
    },
    {
      year: '2017',
      title: 'Primer Éxito',
      description: 'Lanza "Atraco Con 2" en colaboración con Pablo Chill-E y Polima WestCoast, estableciendo su presencia en el reggaetón nacional.',
      image: '/placeholder-timeline-2.jpg'
    },
    {
      year: '2019',
      title: 'Firma con Rimas Entertainment',
      description: 'Firma con Nabru Records, filial chilena de Rimas Entertainment (sello de <PERSON> Bunny), marcando un ascenso profesional significativo.',
      image: '/placeholder-timeline-3.jpg'
    },
    {
      year: '2020',
      title: 'Romeo sin Julieta',
      description: 'Lanza su primer EP conceptual "<PERSON> sin Julieta", una reimaginación urbana de estos personajes viviendo la pandemia.',
      image: '/placeholder-timeline-4.jpg'
    },
    {
      year: '2021',
      title: 'Inmortales',
      description: 'Lanza el álbum "Inmortales" en colaboración con AK4:20, consolidando su presencia en la escena urbana con hits como "TOTA".',
      image: '/placeholder-timeline-5.jpg'
    },
    {
      year: '2024',
      title: 'Era Mojaera',
      description: 'Inicia la era "Mojaera" con el lanzamiento de "Mojaita" junto a Kidd Voodoo y Kaydy Cain, expandiendo su alcance internacional.',
      image: '/placeholder-timeline-6.jpg'
    }
  ]

  const achievements = [
    'Co-fundador del influyente colectivo Shishigang',
    'Más de 168 millones de reproducciones en Spotify',
    'Artista firmado con Rimas Entertainment (sello de Bad Bunny)',
    'Reconocido como "máquina para hacer hits de reggaetón"',
    'Colaboraciones con artistas internacionales como Kaydy Cain',
    'Más de 91 millones de reproducciones como artista principal',
    'Creador de álbumes conceptuales innovadores',
    'Influencia clave en el desarrollo del reggaetón chileno'
  ]

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-red-black">
        <Container>
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
                La Historia de <span className="text-drago-red">Drago200</span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Conoce la historia del artista que revolucionó la música urbana en Chile 
                y se convirtió en el padre del reggaetón chileno.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button size="lg" glow>
                  Escuchar Música
                </Button>
                <Button variant="outline" size="lg">
                  Descargar Press Kit
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gradient-red-gold rounded-2xl p-8 shadow-glow-red">
                <div className="w-full h-full bg-drago-black rounded-xl flex items-center justify-center">
                  <span className="text-6xl font-bold text-drago-red">D200</span>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Navigation Tabs */}
      <section className="py-8 bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex flex-wrap gap-4 justify-center">
            {[
              { id: 'historia', label: 'Historia' },
              { id: 'logros', label: 'Logros' },
              { id: 'timeline', label: 'Cronología' }
            ].map((tab) => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? 'primary' : 'ghost'}
                onClick={() => setActiveTab(tab.id)}
                className="min-w-[120px]"
              >
                {tab.label}
              </Button>
            ))}
          </div>
        </Container>
      </section>

      {/* Content Sections */}
      <section className="section-padding">
        <Container>
          {/* Historia */}
          {activeTab === 'historia' && (
            <div className="max-w-4xl mx-auto">
              <Card variant="glass" className="p-8 mb-8">
                <h2 className="text-3xl font-bold text-white mb-6">Juan Francisco David Vásquez López: El Visionario del Reggaetón Chileno</h2>
                <div className="prose prose-lg prose-invert max-w-none">
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    <strong>Drago200</strong>, cuyo nombre real es Juan Francisco David Vásquez López, se ha consolidado como una figura central
                    y un compositor influyente en la vibrante escena del reggaetón chileno. Su trayectoria se distingue por una capacidad
                    constante para producir éxitos, lo que le ha valido la descripción de ser una "máquina para hacer hits de reggaetón".
                  </p>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    La incursión de Drago200 en el ámbito musical comenzó alrededor del año <strong>2016</strong>, marcando el inicio de una carrera
                    que rápidamente lo establecería como un referente. Un pilar fundamental en sus inicios fue su rol como co-fundador de
                    <strong>Shishigang</strong>, un influyente colectivo chileno de música urbana que fomentó la colaboración y el apoyo mutuo
                    que aceleraron el crecimiento de la escena.
                  </p>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Un momento crucial en su carrera se produjo en <strong>2019</strong>, cuando Drago200 firmó con <strong>Nabru Records</strong>,
                    una filial chilena de Rimas Entertainment, el mismo sello discográfico que es hogar de superestrellas globales como Bad Bunny.
                    Esta afiliación validó su talento y su potencial en el mercado internacional.
                  </p>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Con más de <strong>168 millones de reproducciones totales en Spotify</strong> y más de <strong>91 millones como artista principal</strong>,
                    Drago200 ha demostrado un impacto comercial considerable. Su visión artística singular y su papel en la configuración del sonido
                    contemporáneo de la música urbana chilena lo posicionan como un verdadero innovador cuyo objetivo es "trascender positivamente".
                  </p>
                </div>
              </Card>
            </div>
          )}

          {/* Logros */}
          {activeTab === 'logros' && (
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-white mb-8 text-center">Logros y Reconocimientos</h2>
              <div className="grid md:grid-cols-2 gap-6">
                {achievements.map((achievement, index) => (
                  <Card key={index} variant="glass" className="p-6 flex items-center space-x-4">
                    <div className="w-12 h-12 bg-drago-red rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-white font-medium">{achievement}</p>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Timeline */}
          {activeTab === 'timeline' && (
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-white mb-12 text-center">Cronología Musical</h2>
              <div className="space-y-8">
                {timelineEvents.map((event, index) => (
                  <div key={index} className="flex flex-col md:flex-row gap-6 items-start">
                    <div className="md:w-24 flex-shrink-0">
                      <div className="bg-drago-red text-white px-4 py-2 rounded-lg font-bold text-center">
                        {event.year}
                      </div>
                    </div>
                    <Card variant="glass" className="flex-1 p-6">
                      <h3 className="text-xl font-bold text-white mb-3">{event.title}</h3>
                      <p className="text-gray-300 leading-relaxed">{event.description}</p>
                    </Card>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Container>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-drago-black-light">
        <Container>
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Descubre la Música de Drago200
            </h2>
            <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
              Explora la discografía completa del pionero del reggaetón chileno
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" glow>
                Ir a Música
              </Button>
              <Button variant="outline" size="lg">
                Ver Press Kit
              </Button>
            </div>
          </div>
        </Container>
      </section>
    </div>
  )
}
