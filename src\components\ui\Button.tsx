import React from 'react'
import { cn } from '@/utils/cn'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  glow?: boolean
  children: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', glow = false, children, ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-drago-red disabled:opacity-50 disabled:cursor-not-allowed'
    
    const variants = {
      primary: 'bg-drago-red text-white hover:bg-drago-red-dark hover:shadow-glow-red',
      secondary: 'bg-drago-black-light text-white border border-gray-600 hover:border-drago-red hover:text-drago-red',
      ghost: 'bg-transparent text-white hover:bg-drago-red hover:text-white',
      outline: 'bg-transparent border-2 border-drago-red text-drago-red hover:bg-drago-red hover:text-white'
    }
    
    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
      xl: 'px-8 py-4 text-xl'
    }
    
    const glowClass = glow ? 'shadow-glow-red' : ''
    
    return (
      <button
        className={cn(
          baseClasses,
          variants[variant],
          sizes[size],
          glowClass,
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'

export default Button
