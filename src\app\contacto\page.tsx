'use client'

import { useState } from 'react'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export default function ContactoPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const contactTypes = [
    { id: 'general', label: 'Consulta General' },
    { id: 'booking', label: 'Contrataciones' },
    { id: 'press', label: 'Prensa' },
    { id: 'collaboration', label: 'Colaboraciones' },
    { id: 'business', label: 'Negocios' }
  ]

  const contactInfo = [
    {
      title: 'Management',
      details: [
        'Email: <EMAIL>',
        'Teléfono: +56 9 XXXX XXXX'
      ],
      icon: '👤'
    },
    {
      title: 'Booking & Shows',
      details: [
        'Email: <EMAIL>',
        'Teléfono: +56 9 XXXX XXXX'
      ],
      icon: '🎤'
    },
    {
      title: 'Prensa & Media',
      details: [
        'Email: <EMAIL>',
        'Teléfono: +56 9 XXXX XXXX'
      ],
      icon: '📰'
    },
    {
      title: 'Colaboraciones',
      details: [
        'Email: <EMAIL>',
        'A&R: +56 9 XXXX XXXX'
      ],
      icon: '🤝'
    }
  ]

  const socialLinks = [
    { name: 'Instagram', url: '#', icon: '📷' },
    { name: 'YouTube', url: '#', icon: '📺' },
    { name: 'Spotify', url: '#', icon: '🎵' },
    { name: 'TikTok', url: '#', icon: '🎬' },
    { name: 'Twitter', url: '#', icon: '🐦' }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simular envío del formulario
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setSubmitted(true)
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: '',
      type: 'general'
    })
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-red-black">
        <Container>
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Contacto
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              ¿Tienes una propuesta, consulta o quieres colaborar? 
              Estamos aquí para escucharte. Conecta con el equipo de Drago200.
            </p>
          </div>
        </Container>
      </section>

      {/* Contact Form & Info */}
      <section className="section-padding">
        <Container>
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-white mb-8">Envíanos un Mensaje</h2>
              
              {submitted ? (
                <Card variant="glass" className="p-8 text-center">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">¡Mensaje Enviado!</h3>
                  <p className="text-gray-300 mb-6">
                    Gracias por contactarnos. Te responderemos lo antes posible.
                  </p>
                  <Button onClick={() => setSubmitted(false)} variant="outline">
                    Enviar Otro Mensaje
                  </Button>
                </Card>
              ) : (
                <Card variant="glass" className="p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Contact Type */}
                    <div>
                      <label className="block text-white font-semibold mb-2">
                        Tipo de Consulta
                      </label>
                      <select
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                        required
                      >
                        {contactTypes.map(type => (
                          <option key={type.id} value={type.id}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Name */}
                    <div>
                      <label className="block text-white font-semibold mb-2">
                        Nombre Completo
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Tu nombre completo"
                        className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                        required
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <label className="block text-white font-semibold mb-2">
                        Email
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                        className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                        required
                      />
                    </div>

                    {/* Subject */}
                    <div>
                      <label className="block text-white font-semibold mb-2">
                        Asunto
                      </label>
                      <input
                        type="text"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        placeholder="Asunto de tu mensaje"
                        className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors"
                        required
                      />
                    </div>

                    {/* Message */}
                    <div>
                      <label className="block text-white font-semibold mb-2">
                        Mensaje
                      </label>
                      <textarea
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        rows={6}
                        placeholder="Escribe tu mensaje aquí..."
                        className="w-full bg-white border border-gray-300 text-black rounded-lg px-4 py-2 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none transition-colors resize-vertical"
                        required
                      />
                    </div>

                    {/* Submit Button */}
                    <Button
                      type="submit"
                      size="lg"
                      glow
                      disabled={isSubmitting}
                      className="w-full"
                    >
                      {isSubmitting ? 'Enviando...' : 'Enviar Mensaje'}
                    </Button>
                  </form>
                </Card>
              )}
            </div>

            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-bold text-white mb-8">Información de Contacto</h2>
              
              <div className="space-y-6 mb-12">
                {contactInfo.map((info, index) => (
                  <Card key={index} variant="glass" className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="text-3xl">{info.icon}</div>
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">{info.title}</h3>
                        {info.details.map((detail, idx) => (
                          <p key={idx} className="text-gray-300 mb-1">{detail}</p>
                        ))}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Social Media */}
              <div>
                <h3 className="text-xl font-bold text-white mb-6">Síguenos en Redes Sociales</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  {socialLinks.map((social) => (
                    <Card key={social.name} hover variant="glass" className="p-4 text-center">
                      <div className="text-2xl mb-2">{social.icon}</div>
                      <p className="text-white font-semibold text-sm">{social.name}</p>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Response Time */}
              <Card variant="glass" className="p-6 mt-8">
                <h3 className="text-lg font-bold text-white mb-3">⏰ Tiempo de Respuesta</h3>
                <div className="space-y-2 text-gray-300">
                  <p>• Consultas generales: 24-48 horas</p>
                  <p>• Booking & Shows: 48-72 horas</p>
                  <p>• Prensa: 24 horas</p>
                  <p>• Colaboraciones: 3-5 días hábiles</p>
                </div>
              </Card>
            </div>
          </div>
        </Container>
      </section>

      {/* FAQ Section */}
      <section className="section-padding bg-drago-black-light">
        <Container>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Preguntas Frecuentes</h2>
            <p className="text-gray-400">Respuestas a las consultas más comunes</p>
          </div>
          
          <div className="max-w-3xl mx-auto space-y-6">
            {[
              {
                question: '¿Cómo puedo contratar a Drago200 para un evento?',
                answer: 'Para contrataciones, envía un <NAME_EMAIL> con los detalles del evento: fecha, lugar, tipo de evento y presupuesto.'
              },
              {
                question: '¿Drago200 acepta colaboraciones con nuevos artistas?',
                answer: 'Sí, siempre estamos abiertos a nuevas colaboraciones. Envía tu <NAME_EMAIL> con tu música y propuesta.'
              },
              {
                question: '¿Cómo puedo obtener material de prensa?',
                answer: 'Todo el material de prensa está disponible en nuestra sección Press Kit o puedes <NAME_EMAIL>.'
              },
              {
                question: '¿Responden a todos los mensajes?',
                answer: 'Sí, respondemos a todos los mensajes. El tiempo de respuesta varía según el tipo de consulta.'
              }
            ].map((faq, index) => (
              <Card key={index} variant="glass" className="p-6">
                <h3 className="text-lg font-bold text-white mb-3">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </Card>
            ))}
          </div>
        </Container>
      </section>
    </div>
  )
}
