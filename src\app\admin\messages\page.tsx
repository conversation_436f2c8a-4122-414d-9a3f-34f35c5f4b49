'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { ContactMessage, User } from '@/types'

export default function MessagesAdminPage() {
  const [messages, setMessages] = useState<ContactMessage[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'new' | 'read' | 'replied' | 'archived'>('all')
  const [selectedMessage, setSelectedMessage] = useState<ContactMessage | null>(null)
  const [replyText, setReplyText] = useState('')
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const router = useRouter()

  useEffect(() => {
    checkAccess()
    loadMessages()
  }, [])

  const checkAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || !['admin', 'moderador'].includes(profile.role) || profile.status !== 'approved') {
      router.push('/admin')
      return
    }

    setCurrentUser(profile)
  }

  const loadMessages = async () => {
    try {
      const { data, error } = await supabase
        .from('contact_messages')
        .select(`
          *,
          replier:profiles!contact_messages_replied_by_fkey(nombres, apellidos)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setMessages(data || [])
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateStatus = async (messageId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('contact_messages')
        .update({ status: newStatus })
        .eq('id', messageId)

      if (error) throw error
      loadMessages()
    } catch (error) {
      console.error('Error updating message status:', error)
    }
  }

  const handleReply = async () => {
    if (!selectedMessage || !replyText.trim()) return

    try {
      const { error } = await supabase
        .from('contact_messages')
        .update({
          status: 'replied',
          reply_message: replyText,
          replied_by: currentUser?.id
        })
        .eq('id', selectedMessage.id)

      if (error) throw error

      setSelectedMessage(null)
      setReplyText('')
      loadMessages()
    } catch (error) {
      console.error('Error sending reply:', error)
    }
  }

  const handleDelete = async (messageId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este mensaje?')) return

    try {
      const { error } = await supabase
        .from('contact_messages')
        .delete()
        .eq('id', messageId)

      if (error) throw error
      loadMessages()
    } catch (error) {
      console.error('Error deleting message:', error)
    }
  }

  const filteredMessages = messages.filter(message => {
    if (filter === 'all') return true
    return message.status === filter
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'text-blue-400'
      case 'read': return 'text-yellow-400'
      case 'replied': return 'text-green-400'
      case 'archived': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-600'
      case 'read': return 'bg-yellow-600'
      case 'replied': return 'bg-green-600'
      case 'archived': return 'bg-gray-600'
      default: return 'bg-gray-600'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'booking': return 'bg-purple-600'
      case 'press': return 'bg-orange-600'
      case 'collaboration': return 'bg-pink-600'
      case 'business': return 'bg-blue-600'
      default: return 'bg-gray-600'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'booking': return '🎪'
      case 'press': return '📰'
      case 'collaboration': return '🤝'
      case 'business': return '💼'
      default: return '💬'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando mensajes...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Gestión de Mensajes</h1>
              <p className="text-gray-400">Administra mensajes de contacto</p>
            </div>
            <Button variant="ghost" onClick={() => router.push('/admin')}>
              ← Dashboard
            </Button>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          {/* Filtros */}
          <div className="flex flex-wrap gap-4 mb-8">
            {[
              { key: 'all', label: 'Todos', count: messages.length },
              { key: 'new', label: 'Nuevos', count: messages.filter(m => m.status === 'new').length },
              { key: 'read', label: 'Leídos', count: messages.filter(m => m.status === 'read').length },
              { key: 'replied', label: 'Respondidos', count: messages.filter(m => m.status === 'replied').length },
              { key: 'archived', label: 'Archivados', count: messages.filter(m => m.status === 'archived').length }
            ].map(filterOption => (
              <Button
                key={filterOption.key}
                variant={filter === filterOption.key ? 'primary' : 'ghost'}
                onClick={() => setFilter(filterOption.key as any)}
                size="sm"
              >
                {filterOption.label} ({filterOption.count})
              </Button>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Lista de Mensajes */}
            <div className="lg:col-span-2 space-y-4">
              {filteredMessages.map(message => (
                <Card 
                  key={message.id} 
                  variant="glass" 
                  className={`p-6 cursor-pointer transition-all ${
                    selectedMessage?.id === message.id ? 'ring-2 ring-drago-red' : ''
                  }`}
                  onClick={() => {
                    setSelectedMessage(message)
                    if (message.status === 'new') {
                      handleUpdateStatus(message.id, 'read')
                    }
                  }}
                >
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="text-2xl">{getTypeIcon(message.type)}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-white">
                            {message.subject}
                          </h3>
                          <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getTypeColor(message.type)}`}>
                            {message.type}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getStatusBadgeColor(message.status)}`}>
                            {message.status}
                          </span>
                        </div>
                        
                        <div className="text-sm text-gray-300 mb-2">
                          <span className="font-medium">{message.name}</span> - {message.email}
                        </div>
                        
                        <p className="text-gray-300 text-sm line-clamp-2 mb-2">
                          {message.message}
                        </p>
                        
                        <div className="text-xs text-gray-400">
                          {new Date(message.created_at).toLocaleDateString('es-CL', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2">
                      <select
                        value={message.status}
                        onChange={(e) => {
                          e.stopPropagation()
                          handleUpdateStatus(message.id, e.target.value)
                        }}
                        className="px-2 py-1 bg-drago-black-light border border-gray-600 text-white rounded text-xs"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <option value="new">Nuevo</option>
                        <option value="read">Leído</option>
                        <option value="replied">Respondido</option>
                        <option value="archived">Archivado</option>
                      </select>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(message.id)
                        }}
                        className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white text-xs"
                      >
                        🗑️
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}

              {filteredMessages.length === 0 && (
                <Card variant="glass" className="p-8 text-center">
                  <p className="text-gray-400">No hay mensajes que coincidan con el filtro seleccionado.</p>
                </Card>
              )}
            </div>

            {/* Panel de Detalle */}
            <div className="space-y-6">
              {selectedMessage ? (
                <>
                  <Card variant="glass" className="p-6">
                    <h2 className="text-xl font-bold text-white mb-4">Detalle del Mensaje</h2>
                    
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-semibold text-white">{selectedMessage.subject}</h3>
                        <div className="flex gap-2 mt-2">
                          <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getTypeColor(selectedMessage.type)}`}>
                            {selectedMessage.type}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getStatusBadgeColor(selectedMessage.status)}`}>
                            {selectedMessage.status}
                          </span>
                        </div>
                      </div>

                      <div className="border-t border-gray-600 pt-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-400">De:</span>
                            <p className="text-white">{selectedMessage.name}</p>
                          </div>
                          <div>
                            <span className="text-gray-400">Email:</span>
                            <p className="text-white">{selectedMessage.email}</p>
                          </div>
                          <div className="col-span-2">
                            <span className="text-gray-400">Fecha:</span>
                            <p className="text-white">
                              {new Date(selectedMessage.created_at).toLocaleDateString('es-CL', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="border-t border-gray-600 pt-4">
                        <span className="text-gray-400 text-sm">Mensaje:</span>
                        <p className="text-white mt-2 whitespace-pre-wrap">{selectedMessage.message}</p>
                      </div>

                      {selectedMessage.reply_message && (
                        <div className="border-t border-gray-600 pt-4">
                          <span className="text-gray-400 text-sm">Respuesta enviada:</span>
                          <p className="text-white mt-2 whitespace-pre-wrap">{selectedMessage.reply_message}</p>
                          {selectedMessage.replier && (
                            <p className="text-gray-400 text-xs mt-2">
                              Por: {selectedMessage.replier.nombres} {selectedMessage.replier.apellidos}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </Card>

                  {/* Formulario de Respuesta */}
                  {selectedMessage.status !== 'replied' && (
                    <Card variant="glass" className="p-6">
                      <h3 className="text-lg font-bold text-white mb-4">Responder</h3>
                      
                      <div className="space-y-4">
                        <textarea
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          rows={6}
                          className="w-full px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded focus:border-drago-red focus:outline-none"
                          placeholder="Escribe tu respuesta aquí..."
                        />
                        
                        <div className="flex gap-2">
                          <Button
                            variant="primary"
                            onClick={handleReply}
                            disabled={!replyText.trim()}
                            className="flex-1"
                          >
                            Enviar Respuesta
                          </Button>
                          <Button
                            variant="ghost"
                            onClick={() => setReplyText('')}
                          >
                            Limpiar
                          </Button>
                        </div>
                      </div>
                    </Card>
                  )}
                </>
              ) : (
                <Card variant="glass" className="p-6 text-center">
                  <div className="text-4xl mb-4">💬</div>
                  <h3 className="text-lg font-semibold text-white mb-2">Selecciona un mensaje</h3>
                  <p className="text-gray-400 text-sm">
                    Haz clic en un mensaje de la lista para ver los detalles y responder
                  </p>
                </Card>
              )}
            </div>
          </div>
        </Container>
      </main>
    </div>
  )
}
