import type { Metadata } from "next";
import { <PERSON>, <PERSON> } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/layout/Navigation";
import Footer from "@/components/layout/Footer";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const oswald = <PERSON>({
  variable: "--font-oswald",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Drago200 - El Padre del Reggaetón Chileno",
  description: "Sitio web oficial de Drago200, pionero del reggaetón en Chile. Descubre su música, biografía, noticias y más.",
  keywords: "Drago200, reggaetón chileno, música urbana, Chile, pionero reggaetón",
  authors: [{ name: "Drago200" }],
  creator: "Drago200",
  publisher: "Drago200 Official",
  openGraph: {
    title: "Drago200 - El Padre del Reggaetón Chileno",
    description: "Sitio web oficial de Drago200, pionero del reggaetón en Chile.",
    url: "https://drago200.com",
    siteName: "Drago200 Official",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Drago200 - El Padre del Reggaetón Chileno",
      },
    ],
    locale: "es_CL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Drago200 - El Padre del Reggaetón Chileno",
    description: "Sitio web oficial de Drago200, pionero del reggaetón en Chile.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" className="scroll-smooth">
      <body className={`${inter.variable} ${oswald.variable} antialiased`}>
        <Navigation />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
