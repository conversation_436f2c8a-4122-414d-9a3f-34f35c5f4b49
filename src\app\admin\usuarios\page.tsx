'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { supabase } from '@/lib/supabase'
import { User } from '@/types'

export default function UsuariosAdminPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')
  const [currentUser, setCurrentUser] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    checkAdminAccess()
    loadUsers()
  }, [])

  const checkAdminAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/auth/login')
      return
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (!profile || profile.role !== 'admin' || profile.status !== 'approved') {
      router.push('/')
      return
    }

    setCurrentUser(profile)
  }

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error loading users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUserAction = async (userId: string, action: 'approve' | 'reject', role?: string) => {
    try {
      const updateData: any = {
        status: action === 'approve' ? 'approved' : 'rejected',
        approved_by: currentUser?.id,
        approved_at: new Date().toISOString()
      }

      if (action === 'approve' && role) {
        updateData.role = role
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId)

      if (error) throw error

      // Recargar usuarios
      loadUsers()
    } catch (error) {
      console.error('Error updating user:', error)
    }
  }

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole })
        .eq('id', userId)

      if (error) throw error
      loadUsers()
    } catch (error) {
      console.error('Error updating role:', error)
    }
  }

  const filteredUsers = users.filter(user => {
    if (filter === 'all') return true
    return user.status === filter
  })

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-600'
      case 'moderador': return 'bg-blue-600'
      case 'periodista': return 'bg-green-600'
      case 'booking': return 'bg-purple-600'
      case 'manager': return 'bg-orange-600'
      case 'pending': return 'bg-gray-600'
      default: return 'bg-gray-600'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-400'
      case 'rejected': return 'text-red-400'
      case 'pending': return 'text-yellow-400'
      default: return 'text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-drago-black flex items-center justify-center">
        <div className="text-white text-xl">Cargando usuarios...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Header */}
      <header className="bg-drago-black-light border-b border-gray-700">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-2xl font-bold text-white">Gestión de Usuarios</h1>
              <p className="text-gray-400">Administra registros y roles del equipo</p>
            </div>
            <Button variant="ghost" onClick={() => router.push('/admin')}>
              ← Volver al Dashboard
            </Button>
          </div>
        </Container>
      </header>

      <main className="section-padding">
        <Container>
          {/* Filtros */}
          <div className="flex flex-wrap gap-4 mb-8">
            {[
              { key: 'all', label: 'Todos', count: users.length },
              { key: 'pending', label: 'Pendientes', count: users.filter(u => u.status === 'pending').length },
              { key: 'approved', label: 'Aprobados', count: users.filter(u => u.status === 'approved').length },
              { key: 'rejected', label: 'Rechazados', count: users.filter(u => u.status === 'rejected').length }
            ].map(filterOption => (
              <Button
                key={filterOption.key}
                variant={filter === filterOption.key ? 'primary' : 'ghost'}
                onClick={() => setFilter(filterOption.key as any)}
                size="sm"
              >
                {filterOption.label} ({filterOption.count})
              </Button>
            ))}
          </div>

          {/* Lista de Usuarios */}
          <div className="space-y-4">
            {filteredUsers.map(user => (
              <Card key={user.id} variant="glass" className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">
                        {user.nombres} {user.apellidos}
                      </h3>
                      <span className={`px-2 py-1 rounded text-xs font-semibold text-white ${getRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                      <span className={`text-sm font-medium ${getStatusColor(user.status)}`}>
                        {user.status}
                      </span>
                    </div>
                    
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-300">
                      <div>
                        <span className="text-gray-400">Email:</span> {user.email}
                      </div>
                      <div>
                        <span className="text-gray-400">País:</span> {user.pais}
                      </div>
                      <div>
                        <span className="text-gray-400">Edad:</span> {user.edad} años
                      </div>
                      <div>
                        <span className="text-gray-400">Registro:</span> {new Date(user.created_at).toLocaleDateString('es-CL')}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2">
                    {user.status === 'pending' && (
                      <>
                        <select
                          className="px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded text-sm"
                          onChange={(e) => {
                            if (e.target.value) {
                              handleUserAction(user.id, 'approve', e.target.value)
                            }
                          }}
                          defaultValue=""
                        >
                          <option value="">Aprobar como...</option>
                          <option value="moderador">Moderador</option>
                          <option value="periodista">Periodista</option>
                          <option value="booking">Booking</option>
                          <option value="manager">Manager</option>
                        </select>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUserAction(user.id, 'reject')}
                        >
                          Rechazar
                        </Button>
                      </>
                    )}

                    {user.status === 'approved' && user.role !== 'admin' && (
                      <select
                        className="px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded text-sm"
                        value={user.role}
                        onChange={(e) => handleRoleChange(user.id, e.target.value)}
                      >
                        <option value="moderador">Moderador</option>
                        <option value="periodista">Periodista</option>
                        <option value="booking">Booking</option>
                        <option value="manager">Manager</option>
                      </select>
                    )}

                    {user.status === 'rejected' && (
                      <select
                        className="px-3 py-2 bg-drago-black-light border border-gray-600 text-white rounded text-sm"
                        onChange={(e) => {
                          if (e.target.value) {
                            handleUserAction(user.id, 'approve', e.target.value)
                          }
                        }}
                        defaultValue=""
                      >
                        <option value="">Reactivar como...</option>
                        <option value="moderador">Moderador</option>
                        <option value="periodista">Periodista</option>
                        <option value="booking">Booking</option>
                        <option value="manager">Manager</option>
                      </select>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {filteredUsers.length === 0 && (
            <Card variant="glass" className="p-8 text-center">
              <p className="text-gray-400">No hay usuarios que coincidan con el filtro seleccionado.</p>
            </Card>
          )}
        </Container>
      </main>
    </div>
  )
}
