'use client'

import { useState } from 'react'
import Container from '@/components/layout/Container'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export default function MusicaPage() {
  const [activeFilter, setActiveFilter] = useState('todos')
  const [isPlaying, setIsPlaying] = useState<string | null>(null)

  const songs = [
    {
      id: '1',
      title: 'FULL PIOLI 2.O',
      album: 'Single',
      year: '2023',
      duration: '3:45',
      genre: 'reggaeton',
      featured: true,
      cover: '/placeholder-album-1.jpg',
      streams: '33.1M'
    },
    {
      id: '2',
      title: 'TOTA',
      album: 'INMORTALES',
      year: '2021',
      duration: '3:28',
      genre: 'reggaeton',
      featured: true,
      cover: '/placeholder-album-2.jpg',
      streams: '30.0M'
    },
    {
      id: '3',
      title: 'No Está Interesada',
      album: 'Single',
      year: '2021',
      duration: '3:52',
      genre: 'reggaeton',
      featured: true,
      cover: '/placeholder-album-3.jpg',
      streams: '24.0M'
    },
    {
      id: '4',
      title: '<PERSON>',
      album: 'Single',
      year: '2022',
      duration: '4:05',
      genre: 'reggaeton',
      featured: true,
      cover: '/placeholder-album-4.jpg',
      streams: '15.2M'
    },
    {
      id: '5',
      title: 'Atraco Con 2 - Remix',
      album: 'Single',
      year: '2017',
      duration: '3:33',
      genre: 'reggaeton',
      featured: true,
      cover: '/placeholder-album-5.jpg',
      streams: '10.9M'
    },
    {
      id: '6',
      title: 'PAL \'93',
      album: 'INMORTALES',
      year: '2021',
      duration: '3:15',
      genre: 'reggaeton',
      featured: false,
      cover: '/placeholder-album-6.jpg',
      streams: '7.2M'
    },
    {
      id: '7',
      title: 'A la Calma',
      album: 'Single',
      year: '2022',
      duration: '3:42',
      genre: 'reggaeton',
      featured: false,
      cover: '/placeholder-album-7.jpg',
      streams: '5.2M'
    },
    {
      id: '8',
      title: 'Mojaita',
      album: 'Mojaera',
      year: '2024',
      duration: '3:18',
      genre: 'reggaeton',
      featured: true,
      cover: '/placeholder-album-8.jpg',
      streams: '873K'
    },
    {
      id: '9',
      title: 'Mulata',
      album: 'Single',
      year: '2023',
      duration: '3:25',
      genre: 'reggaeton',
      featured: false,
      cover: '/placeholder-album-9.jpg',
      streams: '792K'
    }
  ]

  const albums = [
    {
      id: '1',
      title: 'Romeo sin Julieta',
      year: '2020',
      tracks: 8,
      cover: '/placeholder-album-1.jpg',
      description: 'Reimaginación urbana de estos personajes viviendo la pandemia'
    },
    {
      id: '2',
      title: 'INMORTALES',
      year: '2021',
      tracks: 7,
      cover: '/placeholder-album-2.jpg',
      description: 'Álbum colaborativo con AK4:20 que incluye hits como "TOTA"'
    },
    {
      id: '3',
      title: 'Mojaera',
      year: '2024',
      tracks: 13,
      cover: '/placeholder-album-3.jpg',
      description: 'Próximo álbum conceptual: un viaje por diferentes épocas del reggaetón'
    }
  ]

  const filters = [
    { id: 'todos', label: 'Todos' },
    { id: 'reggaeton', label: 'Reggaetón' },
    { id: 'urban', label: 'Urban' },
    { id: 'trap', label: 'Trap' },
    { id: 'featured', label: 'Destacados' }
  ]

  const filteredSongs = songs.filter(song => {
    if (activeFilter === 'todos') return true
    if (activeFilter === 'featured') return song.featured
    return song.genre === activeFilter
  })

  const handlePlay = (songId: string) => {
    setIsPlaying(isPlaying === songId ? null : songId)
  }

  return (
    <div className="min-h-screen bg-drago-black">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-red-black">
        <Container>
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Música de <span className="text-drago-red">Drago200</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Explora la discografía completa del pionero del reggaetón chileno. 
              Desde sus primeros éxitos hasta sus colaboraciones más recientes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" glow>
                ▶ Reproducir Todo
              </Button>
              <Button variant="outline" size="lg">
                Crear Playlist
              </Button>
            </div>
          </div>
        </Container>
      </section>

      {/* Music Player Bar (Fixed) */}
      {isPlaying && (
        <div className="fixed bottom-0 left-0 right-0 bg-drago-black-light border-t border-gray-700 p-4 z-50">
          <Container>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-red-gold rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">♪</span>
                </div>
                <div>
                  <h4 className="text-white font-semibold">
                    {songs.find(s => s.id === isPlaying)?.title}
                  </h4>
                  <p className="text-gray-400 text-sm">Drago200</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm">⏮</Button>
                <Button variant="primary" size="sm" onClick={() => setIsPlaying(null)}>
                  ⏸
                </Button>
                <Button variant="ghost" size="sm">⏭</Button>
              </div>
              <div className="hidden md:flex items-center space-x-2">
                <span className="text-gray-400 text-sm">1:23</span>
                <div className="w-32 h-1 bg-gray-600 rounded-full">
                  <div className="w-1/3 h-full bg-drago-red rounded-full"></div>
                </div>
                <span className="text-gray-400 text-sm">3:45</span>
              </div>
            </div>
          </Container>
        </div>
      )}

      {/* Filters */}
      <section className="py-8 bg-drago-black-light">
        <Container>
          <div className="flex flex-wrap gap-4 justify-center">
            {filters.map((filter) => (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? 'primary' : 'ghost'}
                onClick={() => setActiveFilter(filter.id)}
                size="sm"
              >
                {filter.label}
              </Button>
            ))}
          </div>
        </Container>
      </section>

      {/* Songs List */}
      <section className="section-padding">
        <Container>
          <h2 className="text-3xl font-bold text-white mb-8">Canciones</h2>
          <div className="space-y-4">
            {filteredSongs.map((song, index) => (
              <Card key={song.id} variant="glass" className="p-4 hover:bg-drago-black-light/50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-4 flex-1">
                    <span className="text-gray-400 w-8 text-center">{index + 1}</span>
                    <div className="w-12 h-12 bg-gradient-red-gold rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold">♪</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white font-semibold">{song.title}</h3>
                      <p className="text-gray-400 text-sm">{song.album} • {song.year} • {song.streams} streams</p>
                    </div>
                  </div>
                  <div className="hidden md:block text-gray-400 text-sm">
                    {song.duration}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePlay(song.id)}
                    className="w-10 h-10 rounded-full"
                  >
                    {isPlaying === song.id ? '⏸' : '▶'}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </Container>
      </section>

      {/* Albums Section */}
      <section className="section-padding bg-drago-black-light">
        <Container>
          <h2 className="text-3xl font-bold text-white mb-8">Álbumes</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {albums.map((album) => (
              <Card key={album.id} hover variant="glass" className="overflow-hidden">
                <div className="aspect-square bg-gradient-red-gold p-8">
                  <div className="w-full h-full bg-drago-black rounded-lg flex items-center justify-center">
                    <span className="text-3xl font-bold text-drago-red">♪</span>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-white font-bold mb-1">{album.title}</h3>
                  <p className="text-gray-400 text-sm mb-2">{album.year} • {album.tracks} tracks</p>
                  <p className="text-gray-300 text-sm mb-4">{album.description}</p>
                  <Button variant="ghost" size="sm" className="w-full">
                    ▶ Reproducir
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </Container>
      </section>

      {/* Streaming Platforms */}
      <section className="section-padding">
        <Container>
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Escucha en tus Plataformas Favoritas
            </h2>
            <p className="text-gray-400 mb-8">
              La música de Drago200 está disponible en todas las plataformas digitales
            </p>
            <div className="flex flex-wrap gap-4 justify-center">
              {['Spotify', 'Apple Music', 'YouTube Music', 'Deezer', 'Amazon Music'].map((platform) => (
                <Button key={platform} variant="outline" size="lg">
                  {platform}
                </Button>
              ))}
            </div>
          </div>
        </Container>
      </section>
    </div>
  )
}
