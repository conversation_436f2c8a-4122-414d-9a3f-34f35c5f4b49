# Configuración de Supabase para Drago200 Website

## 1. Ejecutar el Schema de Base de Datos

1. Ve a tu proyecto de Supabase: https://supabase.com/dashboard/projects
2. Navega a **SQL Editor**
3. Copia y pega el contenido completo del archivo `database/schema.sql`
4. Ejecuta el script haciendo clic en **Run**

## 2. Configurar Autenticación con Google

1. Ve a **Authentication** > **Providers**
2. Habilita **Google** como proveedor
3. Configura las credenciales de Google OAuth:
   - Ve a [Google Cloud Console](https://console.cloud.google.com/)
   - Crea un nuevo proyecto o selecciona uno existente
   - Habilita la API de Google+
   - Ve a **Credentials** > **Create Credentials** > **OAuth 2.0 Client ID**
   - Configura las URLs de redirección:
     - `https://[tu-proyecto].supabase.co/auth/v1/callback`
     - `http://localhost:3001/auth/callback` (para desarrollo)
   - Copia el **Client ID** y **Client Secret** a Supabase

## 3. Configurar Row Level Security (RLS)

El schema ya incluye las políticas RLS, pero verifica que estén activas:

1. Ve a **Database** > **Tables**
2. Para cada tabla, verifica que **RLS enabled** esté activado
3. Revisa las políticas en la pestaña **Policies**

## 4. Crear Usuario Administrador Inicial

1. Ve al **SQL Editor**
2. Ejecuta esta consulta reemplazando `<EMAIL>` con tu email:

```sql
-- Primero, regístrate en la aplicación con Google OAuth
-- Luego ejecuta esta consulta para convertirte en admin

UPDATE profiles 
SET role = 'admin', status = 'approved', approved_at = NOW()
WHERE email = '<EMAIL>';
```

## 5. Configurar Variables de Entorno

Asegúrate de que tu archivo `.env.local` tenga:

```env
NEXT_PUBLIC_SUPABASE_URL=https://[tu-proyecto].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=[tu-anon-key]
SUPABASE_SERVICE_ROLE_KEY=[tu-service-role-key]
```

## 6. Configurar Storage (Opcional)

Para subir imágenes y archivos:

1. Ve a **Storage**
2. Crea buckets para:
   - `avatars` (fotos de perfil)
   - `blog-images` (imágenes del blog)
   - `song-covers` (portadas de canciones)
   - `press-kit` (archivos del press kit)

3. Configura políticas de acceso para cada bucket

## 7. Flujo de Trabajo del Sistema

### Registro de Nuevos Usuarios:
1. Usuario completa formulario en `/registro`
2. Se crea perfil con `status: 'pending'` y `role: 'pending'`
3. Admin revisa en `/admin/usuarios`
4. Admin aprueba y asigna rol específico
5. Usuario puede hacer login y acceder según su rol

### Roles y Permisos:
- **Admin**: Acceso completo, puede gestionar usuarios
- **Moderador**: Puede gestionar blog y contenido general
- **Periodista**: Puede crear y editar posts del blog
- **Booking**: Puede gestionar eventos
- **Manager**: Puede gestionar catálogo de música

### Gestión de Contenido:
- Blog posts se crean desde `/admin/blog/nuevo`
- Solo usuarios con roles apropiados pueden crear contenido
- Todo el contenido pasa por moderación antes de publicarse

## 8. Verificación de la Configuración

1. Inicia la aplicación: `npm run dev`
2. Ve a `/registro` y completa un registro de prueba
3. Ve a `/admin/usuarios` (como admin) y aprueba el registro
4. Verifica que el login funcione correctamente
5. Prueba crear un post en `/admin/blog/nuevo`

## 9. Troubleshooting

### Error de conexión a Supabase:
- Verifica que las URLs y keys sean correctas
- Asegúrate de que el proyecto de Supabase esté activo

### Error de permisos:
- Verifica que RLS esté configurado correctamente
- Revisa que el usuario tenga el rol y status apropiados

### Error de autenticación:
- Verifica la configuración de Google OAuth
- Asegúrate de que las URLs de callback sean correctas

## 10. Datos de Prueba (Opcional)

Puedes insertar algunos datos de prueba ejecutando:

```sql
-- Insertar algunas canciones de ejemplo
INSERT INTO songs (title, artist, album, genre, featured, status, spotify_streams) VALUES
('FULL PIOLI 2.O', 'Drago200', 'Single', 'reggaeton', true, 'published', 33100000),
('TOTA', 'Drago200 x AK4:20', 'INMORTALES', 'reggaeton', true, 'published', 30000000),
('No Está Interesada', 'Drago200', 'Single', 'reggaeton', true, 'published', 24000000);

-- Insertar un post de blog de ejemplo
INSERT INTO blog_posts (title, slug, content, excerpt, category, published, author_id) VALUES
('Bienvenidos al nuevo sitio web', 'bienvenidos-nuevo-sitio', 
'Contenido del post...', 'Extracto del post...', 'noticias', true,
(SELECT id FROM profiles WHERE role = 'admin' LIMIT 1));
```
