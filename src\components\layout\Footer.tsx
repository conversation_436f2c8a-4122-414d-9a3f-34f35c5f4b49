import Link from 'next/link'
import Container from './Container'
import Button from '../ui/Button'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  const footerSections = [
    {
      title: 'Navegación',
      links: [
        { name: 'Inicio', href: '/' },
        { name: 'Biografía', href: '/biografia' },
        { name: '<PERSON><PERSON><PERSON>', href: '/musica' },
        { name: 'Press Kit', href: '/press-kit' }
      ]
    },
    {
      title: 'Contenido',
      links: [
        { name: 'Blog', href: '/blog' },
        { name: 'Noticias', href: '/blog?category=noticias' },
        { name: 'Eventos', href: '/blog?category=eventos' },
        { name: 'Contacto', href: '/contacto' }
      ]
    },
    {
      title: 'Legal',
      links: [
        { name: 'T<PERSON>rm<PERSON><PERSON> de Uso', href: '/legal/terminos' },
        { name: 'Política de Privacidad', href: '/legal/privacidad' },
        { name: 'Cookies', href: '/legal/cookies' },
        { name: 'Derechos de Autor', href: '/legal/copyright' }
      ]
    }
  ]

  const socialLinks = [
    { name: 'Instagram', href: '#', icon: '📷' },
    { name: 'YouTube', href: '#', icon: '📺' },
    { name: 'Spotify', href: '#', icon: '🎵' },
    { name: 'Apple Music', href: '#', icon: '🎶' },
    { name: 'TikTok', href: '#', icon: '🎬' },
    { name: 'Twitter', href: '#', icon: '🐦' }
  ]

  const streamingPlatforms = [
    { name: 'Spotify', href: '#' },
    { name: 'Apple Music', href: '#' },
    { name: 'YouTube Music', href: '#' },
    { name: 'Deezer', href: '#' },
    { name: 'Amazon Music', href: '#' }
  ]

  return (
    <footer className="bg-drago-black-dark border-t border-gray-800">
      {/* Newsletter Section */}
      <section className="py-16 bg-gradient-red-black">
        <Container>
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Mantente Conectado
            </h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Suscríbete para recibir las últimas noticias, lanzamientos y actualizaciones 
              exclusivas de Drago200 directamente en tu email.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="<EMAIL>"
                className="flex-1 px-4 py-3 rounded-lg bg-white border border-gray-300 text-black placeholder-gray-500 focus:border-drago-red focus:ring-1 focus:ring-drago-red focus:outline-none"
              />
              <Button size="lg" glow>
                Suscribirse
              </Button>
            </div>
          </div>
        </Container>
      </section>

      {/* Main Footer Content */}
      <section className="py-16">
        <Container>
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-12 h-12 bg-gradient-red-gold rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">D</span>
                </div>
                <span className="text-white font-bold text-2xl">
                  DRAGO<span className="text-drago-red">200</span>
                </span>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                El pionero del reggaetón chileno. Más de 20 años marcando la pauta 
                en la música urbana nacional e internacional.
              </p>
              
              {/* Social Links */}
              <div className="flex flex-wrap gap-3">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.href}
                    className="w-10 h-10 bg-drago-black-light border border-gray-700 rounded-lg flex items-center justify-center text-gray-400 hover:text-drago-red hover:border-drago-red transition-colors"
                    title={social.name}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>

            {/* Footer Links */}
            {footerSections.map((section) => (
              <div key={section.title}>
                <h3 className="text-white font-bold text-lg mb-4">{section.title}</h3>
                <ul className="space-y-3">
                  {section.links.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-gray-400 hover:text-drago-red transition-colors"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* Streaming Platforms */}
      <section className="py-8 border-t border-gray-800">
        <Container>
          <div className="text-center">
            <h3 className="text-white font-bold text-lg mb-6">
              Escucha en tus Plataformas Favoritas
            </h3>
            <div className="flex flex-wrap gap-4 justify-center">
              {streamingPlatforms.map((platform) => (
                <a
                  key={platform.name}
                  href={platform.href}
                  className="px-4 py-2 bg-drago-black-light border border-gray-700 rounded-lg text-gray-400 hover:text-white hover:border-drago-red transition-colors text-sm"
                >
                  {platform.name}
                </a>
              ))}
            </div>
          </div>
        </Container>
      </section>

      {/* Bottom Bar */}
      <section className="py-6 border-t border-gray-800">
        <Container>
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} Drago200. Todos los derechos reservados.
            </div>
            <div className="flex items-center space-x-6 text-sm">
              <span className="text-gray-400">
                Hecho con ❤️ en Chile
              </span>
              <div className="flex items-center space-x-4">
                <Link href="/legal/terminos" className="text-gray-400 hover:text-drago-red transition-colors">
                  Términos
                </Link>
                <Link href="/legal/privacidad" className="text-gray-400 hover:text-drago-red transition-colors">
                  Privacidad
                </Link>
                <Link href="/contacto" className="text-gray-400 hover:text-drago-red transition-colors">
                  Contacto
                </Link>
              </div>
            </div>
          </div>
        </Container>
      </section>
    </footer>
  )
}
